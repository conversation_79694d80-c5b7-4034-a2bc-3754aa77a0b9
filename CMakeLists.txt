cmake_minimum_required(VERSION 3.16)
project(HVIS_Suite VERSION 1.0 LANGUAGES CXX)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Define options for building tests, docs, and apps
option(BUILD_TESTS "Build tests for all submodules" OFF)
option(BUILD_DOCS "Build documentation for all submodules" OFF)
option(BUILD_APP "Build standalone applications for all submodules" OFF)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Include Conan-generated toolchain
if(EXISTS "${CMAKE_BINARY_DIR}/conan_toolchain.cmake")
    include(${CMAKE_BINARY_DIR}/conan_toolchain.cmake)
endif()

# Find Conan dependencies for HVIS_Suite 
find_package(spdlog REQUIRED)
message(STATUS "Found spdlog: ${spdlog_VERSION}")

# Find GTest package
find_package(GTest REQUIRED)
message(STATUS "Found GTest: ${GTest_VERSION}")

# Include the Qt configuration
include(${CMAKE_SOURCE_DIR}/cmake/QtConfig.cmake)

# Build order: HVIS_Logging first, then other modules
message(STATUS "Building HVIS Suite...")

# Add include and src directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Automatically find all source files
file(GLOB_RECURSE SOURCES CONFIGURE_DEPENDS "src/*.cpp")
file(GLOB_RECURSE HEADERS CONFIGURE_DEPENDS "include/*.h")

# Function to run conan install for submodules
function(run_conan_install submodules_path)
    # Find all subdirectories starting with HVIS_
    file(GLOB HVIS_SUBMODULES RELATIVE ${CMAKE_SOURCE_DIR}/${submodules_path} ${CMAKE_SOURCE_DIR}/${submodules_path}/HVIS_*)
    foreach(submodule ${HVIS_SUBMODULES})
        if(IS_DIRECTORY ${CMAKE_SOURCE_DIR}/${submodules_path}/${submodule})
            # Define output directory for Conan files within main build directory
            set(SUBMODULE_BUILD_DIR ${CMAKE_BINARY_DIR}/${submodules_path}/${submodule})
            file(MAKE_DIRECTORY ${SUBMODULE_BUILD_DIR})
            # Run conan install with output to SUBMODULE_BUILD_DIR
            execute_process(
                COMMAND conan install ${CMAKE_SOURCE_DIR}/${submodules_path}/${submodule} --build=missing --output-folder=${SUBMODULE_BUILD_DIR}
                WORKING_DIRECTORY ${SUBMODULE_BUILD_DIR}
                RESULT_VARIABLE CONAN_RESULT
            )
            if(NOT CONAN_RESULT EQUAL 0)
                message(FATAL_ERROR "Conan install failed for ${submodules_path}/${submodule}")
            endif()
            # Include submodule's Conan toolchain
            if(EXISTS "${SUBMODULE_BUILD_DIR}/conan_toolchain.cmake")
                include(${SUBMODULE_BUILD_DIR}/conan_toolchain.cmake)
            endif()
        endif()
    endforeach()
endfunction()

# Run conan install for submodules
run_conan_install(submodules)

# Include submodules
add_subdirectory(submodules)

message(STATUS "Creating executable...")
add_executable(HVIS_Suite main.cpp ${SOURCES} ${HEADERS})

# Link against the libraries
# Note: We don't need to explicitly link against Qt libraries anymore
# They will be transitively linked through UserInterface
target_link_libraries(${CMAKE_PROJECT_NAME} PRIVATE 
    ImageAcquisition
    UserInterface
    CudaProcessing
    spdlog::spdlog
)

# Include directories
target_include_directories(HVIS_Suite PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/submodules/HVIS_ImageAcquisition/include
    ${CMAKE_SOURCE_DIR}/submodules/HVIS_UserInterface/include
    ${CMAKE_SOURCE_DIR}/submodules/HVIS_CudaProcessing/include
)

# Use our helper function to deploy Qt with custom translations and additional binaries
deploy_qt_for_target(HVIS_Suite 
    TRANSLATIONS 
        "${CMAKE_SOURCE_DIR}/submodules/HVIS_UserInterface/HexcelVisionApp_es_ES.ts"
    ADDITIONAL_BINARIES
        "$<TARGET_FILE:UserInterface>"
)

message(STATUS "HVIS Suite configuration complete")
