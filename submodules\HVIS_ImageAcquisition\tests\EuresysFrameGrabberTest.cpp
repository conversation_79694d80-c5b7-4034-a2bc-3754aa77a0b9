#include "EuresysFrameGrabber.h"
#include <gtest/gtest.h>
#include <memory>
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"

// Setup test environment
class EuresysFrameGrabberTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup loggers for testing
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(spdlog::level::debug);
        
        auto main_logger = std::make_shared<spdlog::logger>("main", console_sink);
        auto euresys_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabber", console_sink);
        auto internal_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabberInternal", console_sink);
        
        spdlog::register_logger(main_logger);
        spdlog::register_logger(euresys_logger);
        spdlog::register_logger(internal_logger);
        
        main_logger->set_level(spdlog::level::debug);
        euresys_logger->set_level(spdlog::level::debug);
        internal_logger->set_level(spdlog::level::debug);
    }

    void TearDown() override {
        // Clean up loggers
        spdlog::drop_all();
    }
};

// Test constructor
TEST_F(EuresysFrameGrabberTest, ConstructorTest) {
    // Create a frame grabber instance
    std::unique_ptr<HVIS::EuresysFrameGrabber> grabber = std::make_unique<HVIS::EuresysFrameGrabber>();
    
    // Verify it was created successfully
    ASSERT_NE(grabber, nullptr);
    
    // Verify initial state
    EXPECT_FALSE(grabber->isInitialized());
    EXPECT_FALSE(grabber->isAcquisitionStarted());
}

// Test callback setting
TEST_F(EuresysFrameGrabberTest, CallbackTest) {
    // Create a frame grabber instance
    std::unique_ptr<HVIS::EuresysFrameGrabber> grabber = std::make_unique<HVIS::EuresysFrameGrabber>();
    
    // Set a callback
    bool callbackCalled = false;
    grabber->setCallback([&callbackCalled](void* bufferData, size_t width, size_t height, size_t size, size_t frameId) {
        callbackCalled = true;
    });
    
    // Note: We can't easily test if the callback is called without actual hardware
    // This test just verifies that setting a callback doesn't crash
    SUCCEED();
}

// Add more tests as needed