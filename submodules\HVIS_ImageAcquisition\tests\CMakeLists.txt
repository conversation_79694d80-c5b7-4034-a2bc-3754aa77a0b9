# Collect all test source files
file(GLOB_RECURSE TEST_SOURCES CONFIGURE_DEPENDS "*.cpp")

# Create test executable
add_executable(ImageAcquisitionTests ${TEST_SOURCES})

# Link against the library and GTest
target_link_libraries(ImageAcquisitionTests PRIVATE 
    ImageAcquisition
    GTest::gtest
    GTest::gtest_main
)

# Add include directories
target_include_directories(ImageAcquisitionTests PRIVATE 
    ${CMAKE_SOURCE_DIR}/include
)

# Register tests with CTest
include(GoogleTest)
gtest_discover_tests(ImageAcquisitionTests)