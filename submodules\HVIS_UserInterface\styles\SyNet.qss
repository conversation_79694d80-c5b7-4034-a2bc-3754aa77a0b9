/*Copyright (c) DevSec Studio. All rights reserved.

MIT License

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

/*-----QWidget-----*/
QWidget
{
	background-color: #1a1a1a;
	color: #000000;

}


/*-----QLabel-----*/
QLabel
{
	background-color: transparent;
	color: #c2c7d5;
	font-size: 13px;

}


/*-----QPushButton-----*/
QPushButton
{
	background-color: #ff3333;
	color: #fff;
	font-size: 11px;
	font-weight: bold;
	border: none;
	border-radius: 25px;
	padding: 5px;

}


QPushButton::disabled
{
	background-color: #5c5c5c;

}


QPushButton::pressed
{
	background-color: #ff4747;

}


/*-----QCheckBox-----*/
QCheckBox
{
	background-color: transparent;
	color: #fff;
	font-size: 10px;
	font-weight: bold;
	border: none;
	border-radius: 5px;

}


/*-----QCheckBox-----*/
QCheckBox::indicator
{
    color: #b1b1b1;
    background-color: #323232;
    border: 1px solid darkgray;
    width: 12px;
    height: 12px;

}


QCheckBox::indicator:checked
{
    image:url("./ressources/check.png");
	background-color: #ff3333;
    border: 1px solid #ff3333;

}


QCheckBox::indicator:unchecked:hover
{
    border: 1px solid #ff3333;

}


QCheckBox::disabled
{
	color: #656565;

}


QCheckBox::indicator:disabled
{
	background-color: #656565;
	color: #656565;
    border: 1px solid #656565;

}


/*-----QLineEdit-----*/
QLineEdit
{
	background-color: #c2c7d5;
	color: #000;
	font-weight: bold;
	border: none;
	border-radius: 2px;
	padding: 3px;

}


/*-----QListView-----*/
QListView
{
	background-color: #333333;
	color: #fff;
	font-size: 12px;
	font-weight: bold;
	border: 1px solid #191919;
	show-decoration-selected: 0;
	padding-left: -13px;
	padding-right: -13px;

}


QListView::item
{
	color: #31cecb;
	background-color: #454e5e;
	border: none;
	padding: 5px;
	border-radius: 0px;
	padding-left : 10px;
	height: 42px;

}

QListView::item:selected
{
	color: #000;
	background-color: #ff3333;

}


QListView::item:!selected
{
	color:white;
	background-color: transparent;
	border: none;
	padding-left : 10px;

}


QListView::item:!selected:hover
{
	color: #000;
	background-color: #bcbdbb;
	border: none;
	padding-left : 10px;

}


/*-----QTreeView-----*/
QTreeView 
{
	background-color: #252525;
	show-decoration-selected: 0;
	color: #c2c8d7;

}


QTreeView::item 
{
	border-top-color: transparent;
	border-bottom-color: transparent;

}


QTreeView::item:hover 
{
	background-color: #bcbdbb;
	color: #000;

}


QTreeView::item:selected 
{
	background-color: #ff3333;
	color: #000;

}


QTreeView::item:selected:active
{
	background-color: #ff3333;
	color: #000;

}


QTreeView::branch:has-children:!has-siblings:closed,
QTreeView::branch:closed:has-children:has-siblings 
{
	image: url(://tree-closed.png);

}

QTreeView::branch:open:has-children:!has-siblings,
QTreeView::branch:open:has-children:has-siblings  
{
	image: url(://tree-open.png);

}


/*-----QTableView & QTableWidget-----*/
QTableView
{
    background-color: #252525;
	border: 1px solid gray;
    color: #f0f0f0;
    gridline-color: gray;
    outline : 0;

}


QTableView::disabled
{
    background-color: #242526;
    border: 1px solid #32414B;
    color: #656565;
    gridline-color: #656565;
    outline : 0;

}


QTableView::item:hover 
{
    background-color: #bcbdbb;
    color: #000;

}


QTableView::item:selected 
{
	background-color: #ff3333;
    color: #000;

}


QTableView::item:selected:disabled
{
    background-color: #1a1b1c;
    border: 2px solid #525251;
    color: #656565;

}


QTableCornerButton::section
{
	background-color: #343a49;
    color: #fff;

}


QHeaderView::section
{
	color: #fff;
	border-top: 0px;
	border-bottom: 1px solid gray;
	border-right: 1px solid gray;
	background-color: #343a49;
    margin-top:1px;
	margin-bottom:1px;
	padding: 5px;

}


QHeaderView::section:disabled
{
    background-color: #525251;
    color: #656565;

}


QHeaderView::section:checked
{
    color: #000;
    background-color: #ff3333;

}


QHeaderView::section:checked:disabled
{
    color: #656565;
    background-color: #525251;

}


QHeaderView::section::vertical::first,
QHeaderView::section::vertical::only-one
{
    border-top: 1px solid #353635;

}


QHeaderView::section::vertical
{
    border-top: 1px solid #353635;

}


QHeaderView::section::horizontal::first,
QHeaderView::section::horizontal::only-one
{
    border-left: 1px solid #353635;

}


QHeaderView::section::horizontal
{
    border-left: 1px solid #353635;

}


/*-----QScrollBar-----*/
QScrollBar:horizontal 
{
    background-color: transparent;
    height: 8px;
    margin: 0px;
    padding: 0px;

}


QScrollBar::handle:horizontal 
{
    border: none;
	min-width: 100px;
    background-color: #9b9b9b;

}


QScrollBar::add-line:horizontal, 
QScrollBar::sub-line:horizontal,
QScrollBar::add-page:horizontal, 
QScrollBar::sub-page:horizontal 
{
    width: 0px;
    background-color: transparent;

}


QScrollBar:vertical 
{
    background-color: transparent;
    width: 8px;
    margin: 0;

}


QScrollBar::handle:vertical 
{
    border: none;
	min-height: 100px;
    background-color: #9b9b9b;

}


QScrollBar::add-line:vertical, 
QScrollBar::sub-line:vertical,
QScrollBar::add-page:vertical, 
QScrollBar::sub-page:vertical 
{
    height: 0px;
    background-color: transparent;

}
