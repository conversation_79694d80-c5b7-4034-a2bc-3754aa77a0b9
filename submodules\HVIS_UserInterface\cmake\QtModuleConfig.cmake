# QtModuleConfig.cmake - Qt configuration for modules/libraries

# Enable Qt features
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Find Qt packages
find_package(Qt6 COMPONENTS Core Widgets LinguistTools REQUIRED)
if(NOT Qt6_FOUND)
    message(FATAL_ERROR "Qt6 not found. Please install Qt6 or set Qt6_DIR.")
endif()

message(STATUS "Found Qt6: ${Qt6_VERSION}")

# Function to set up a Qt-based library
function(setup_qt_library TARGET_NAME)
    cmake_parse_arguments(SETUP "" "UI_DIR" "SOURCES;HEADERS;UI_FILES;PUBLIC_LIBS;PRIVATE_LIBS;PUBLIC_INCLUDES;PRIVATE_INCLUDES;TRANSLATIONS" ${ARGN})
    
    # Set UI search path for AUTOUIC if provided
    if(SETUP_UI_DIR)
        set_target_properties(${TARGET_NAME} PROPERTIES
            AUTOUIC_SEARCH_PATHS ${SETUP_UI_DIR}
        )
    endif()
    
    # Link against Qt libraries
    target_link_libraries(${TARGET_NAME} 
        PUBLIC
            Qt6::Core
            Qt6::Widgets
            ${SETUP_PUBLIC_LIBS}
        PRIVATE
            ${SETUP_PRIVATE_LIBS}
    )
    
    # Set include directories
    target_include_directories(${TARGET_NAME} 
        PUBLIC 
            ${SETUP_PUBLIC_INCLUDES}
        PRIVATE
            ${SETUP_PRIVATE_INCLUDES}
    )
    
    # Handle translations if provided
    if(SETUP_TRANSLATIONS)
        # Find Qt LinguistTools
        find_package(Qt6 COMPONENTS LinguistTools REQUIRED)
        
        # Create output directory for QM files
        set(QM_OUTPUT_DIR "${CMAKE_CURRENT_BINARY_DIR}/translations")
        file(MAKE_DIRECTORY ${QM_OUTPUT_DIR})
        
        # Process each translation file
        foreach(TS_FILE ${SETUP_TRANSLATIONS})
            get_filename_component(TS_BASENAME ${TS_FILE} NAME_WE)
            set(QM_FILE "${QM_OUTPUT_DIR}/${TS_BASENAME}.qm")
            
            # Create custom command to generate QM file
            add_custom_command(
                OUTPUT ${QM_FILE}
                COMMAND ${Qt6_LRELEASE_EXECUTABLE} ${TS_FILE} -qm ${QM_FILE}
                DEPENDS ${TS_FILE}
                COMMENT "Generating QM file from ${TS_FILE}"
            )
            
            # Add QM file to list
            list(APPEND QM_FILES ${QM_FILE})
        endforeach()
        
        # Create custom target for translations
        add_custom_target(${TARGET_NAME}_translations ALL DEPENDS ${QM_FILES})
        
        # Make the main target depend on translations
        add_dependencies(${TARGET_NAME} ${TARGET_NAME}_translations)
        
        # Store QM files location as a property
        set_target_properties(${TARGET_NAME} PROPERTIES
            QT_TRANSLATIONS_DIR "${QM_OUTPUT_DIR}"
            QT_TRANSLATIONS "${QM_FILES}"
        )
    endif()
endfunction()

# Function to set up a Qt-based executable
function(setup_qt_executable TARGET_NAME)
    cmake_parse_arguments(SETUP "WIN32;MACOSX_BUNDLE" "" "SOURCES;LIBS;TRANSLATIONS" ${ARGN})
    
    # Add executable with appropriate flags
    if(SETUP_WIN32 AND WIN32)
        if(SETUP_MACOSX_BUNDLE AND APPLE)
            qt_add_executable(${TARGET_NAME} WIN32 MACOSX_BUNDLE ${SETUP_SOURCES})
        else()
            qt_add_executable(${TARGET_NAME} WIN32 ${SETUP_SOURCES})
        endif()
    elseif(SETUP_MACOSX_BUNDLE AND APPLE)
        qt_add_executable(${TARGET_NAME} MACOSX_BUNDLE ${SETUP_SOURCES})
    else()
        qt_add_executable(${TARGET_NAME} ${SETUP_SOURCES})
    endif()
    
    # Link against libraries
    target_link_libraries(${TARGET_NAME} PRIVATE ${SETUP_LIBS})
    
    # Handle translations if provided
    if(SETUP_TRANSLATIONS)
        qt_add_translations(
            TARGETS ${TARGET_NAME}
            TS_FILES ${SETUP_TRANSLATIONS}
        )
    endif()
    
    # Set up deployment
    if(WIN32)
        # Find windeployqt
        find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS "$ENV{QTDIR}/bin" "${Qt6_DIR}/../../../bin")
        if(NOT WINDEPLOYQT_EXECUTABLE)
            message(WARNING "windeployqt not found. Ensure Qt is installed and QTDIR is set.")
        else()
            # Add post-build command to deploy Qt dependencies
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND "${WINDEPLOYQT_EXECUTABLE}" 
                        --verbose 1
                        --no-compiler-runtime
                        --no-system-d3d-compiler
                        --dir "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                        "$<TARGET_FILE:${TARGET_NAME}>"
                WORKING_DIRECTORY "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                COMMENT "Deploying Qt dependencies with windeployqt for ${TARGET_NAME}"
            )
            
            # Create qt.conf file
            file(GENERATE OUTPUT "$<TARGET_FILE_DIR:${TARGET_NAME}>/qt.conf" 
                 CONTENT "[Paths]\nPlugins = .\nTranslations = ./translations\n")
        endif()
    endif()
endfunction()

# Function to deploy Qt dependencies for both executables and libraries
function(deploy_qt_dependencies TARGET_NAME)
    cmake_parse_arguments(DEPLOY "" "" "ADDITIONAL_BINARIES;TRANSLATIONS" ${ARGN})
    
    if(WIN32)
        # Find windeployqt
        find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS "$ENV{QTDIR}/bin" "${Qt6_DIR}/../../../bin")
        if(NOT WINDEPLOYQT_EXECUTABLE)
            message(WARNING "windeployqt not found. Ensure Qt is installed and QTDIR is set.")
        else()
            # Create a temporary directory for deployment
            set(DEPLOY_TEMP_DIR "${CMAKE_BINARY_DIR}/qt_deploy_temp")
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E make_directory "${DEPLOY_TEMP_DIR}"
                COMMENT "Creating temporary directory for Qt deployment"
            )
            
            # Deploy for main target to the temporary directory
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND "${WINDEPLOYQT_EXECUTABLE}" 
                        --verbose 1
                        --no-compiler-runtime
                        --no-system-d3d-compiler
                        --dir "${DEPLOY_TEMP_DIR}"
                        "$<TARGET_FILE:${TARGET_NAME}>"
                WORKING_DIRECTORY "${DEPLOY_TEMP_DIR}"
                COMMENT "Deploying Qt dependencies with windeployqt for ${TARGET_NAME}"
            )
            
            # Deploy for additional binaries to the temporary directory
            if(DEPLOY_ADDITIONAL_BINARIES)
                foreach(BINARY ${DEPLOY_ADDITIONAL_BINARIES})
                    add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                        COMMAND "${WINDEPLOYQT_EXECUTABLE}" 
                                --verbose 1
                                --no-compiler-runtime
                                --no-system-d3d-compiler
                                --dir "${DEPLOY_TEMP_DIR}"
                                "${BINARY}"
                        WORKING_DIRECTORY "${DEPLOY_TEMP_DIR}"
                        COMMENT "Deploying Qt dependencies with windeployqt for ${BINARY}"
                    )
                endforeach()
            endif()
            
            # Copy all deployed files from temporary directory to target directory
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_directory 
                        "${DEPLOY_TEMP_DIR}" 
                        "$<TARGET_FILE_DIR:${TARGET_NAME}>"
                COMMENT "Copying Qt dependencies from temporary directory to target directory"
            )
            
            # Create translations directory
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:${TARGET_NAME}>/translations"
                COMMENT "Creating translations directory for ${TARGET_NAME}"
            )
            
            # Copy custom translations
            if(DEPLOY_TRANSLATIONS)
                foreach(TRANSLATION_FILE ${DEPLOY_TRANSLATIONS})
                    if(EXISTS "${TRANSLATION_FILE}")
                        get_filename_component(TRANSLATION_FILENAME ${TRANSLATION_FILE} NAME)
                        add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                            COMMAND ${CMAKE_COMMAND} -E copy 
                                    "${TRANSLATION_FILE}" 
                                    "$<TARGET_FILE_DIR:${TARGET_NAME}>/translations/${TRANSLATION_FILENAME}"
                            COMMENT "Copying custom translation file: ${TRANSLATION_FILENAME}"
                        )
                    endif()
                endforeach()
            endif()
                 
            # Clean up temporary directory
            add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E remove_directory "${DEPLOY_TEMP_DIR}"
                COMMENT "Cleaning up temporary Qt deployment directory"
            )
        endif()
    endif()
endfunction()
