#include "ImageCropping.cuh"

__global__ void cropImageKernel(
    const unsigned char* input,
    unsigned char* output,
    int inputWidth,
    int inputHeight,
    int regionWidth,
    int regionHeight,
    int numCols,
    int outputPitch
) {
    // Get the region index and pixel position within region
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    // Calculate which region we're in
    const int regionX = x / regionWidth;
    const int regionY = y / regionHeight;
    
    // Calculate position within the region
    const int localX = x % regionWidth;
    const int localY = y % regionHeight;

    // Early exit if we're outside our target dimensions
    if (regionX >= numCols || x >= inputWidth || y >= inputHeight) return;
    if (localX >= regionWidth || localY >= regionHeight) return;

    // Calculate input and output positions
    const int inputIdx = (y * inputWidth + x) * 3;
    const int outputIdx = (regionY * numCols + regionX) * (regionWidth * regionHeight * 3) +
                         (localY * regionWidth + localX) * 3;

    // Copy RGB values
    for (int c = 0; c < 3; c++) {
        output[outputIdx + c] = input[inputIdx + c];
    }
}

extern "C" void launchCropKernel(
    const unsigned char* d_input,
    unsigned char* d_output,
    int inputWidth,
    int inputHeight,
    int regionWidth,
    int regionHeight,
    int numCols
) {
    // Use block size that's a multiple of warp size (32)
    dim3 blockDim(32, 8);  // 256 threads per block
    
    // Calculate grid size to cover entire input image
    dim3 gridDim(
        (inputWidth + blockDim.x - 1) / blockDim.x,
        (inputHeight + blockDim.y - 1) / blockDim.y
    );

    cropImageKernel<<<gridDim, blockDim>>>(
        d_input,
        d_output,
        inputWidth,
        inputHeight,
        regionWidth,
        regionHeight,
        numCols,
        regionWidth * 3
    );
    // cudaDeviceSynchronize();
}