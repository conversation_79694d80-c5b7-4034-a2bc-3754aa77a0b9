{"artifacts": [{"path": "bin/MinSizeRel/UserInterfaceApp.exe"}, {"path": "bin/MinSizeRel/UserInterfaceApp.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_sources", "_qt_internal_expose_deferred_files_to_ide", "_qt_internal_expose_source_file_to_ide", "qt6_add_translations", "_qt_internal_process_resource", "qt6_add_resources", "__qt_propagate_generated_resource"], "files": ["C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake:817:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 75, "parent": 0}, {"command": 2, "file": 0, "line": 938, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 688, "parent": 3}, {"command": 4, "file": 1, "line": 90, "parent": 0}, {"command": 5, "file": 1, "line": 85, "parent": 0}, {"command": 5, "file": 1, "line": 55, "parent": 0}, {"command": 8, "file": 1, "line": 24, "parent": 0}, {"file": 4, "parent": 8}, {"command": 8, "file": 4, "line": 212, "parent": 9}, {"file": 3, "parent": 10}, {"command": 7, "file": 3, "line": 55, "parent": 11}, {"file": 2, "parent": 12}, {"command": 6, "file": 2, "line": 61, "parent": 13}, {"command": 5, "file": 0, "line": 640, "parent": 2}, {"command": 8, "file": 4, "line": 212, "parent": 9}, {"file": 6, "parent": 16}, {"command": 7, "file": 6, "line": 57, "parent": 17}, {"file": 5, "parent": 18}, {"command": 6, "file": 5, "line": 61, "parent": 19}, {"command": 7, "file": 6, "line": 45, "parent": 17}, {"file": 11, "parent": 21}, {"command": 10, "file": 11, "line": 46, "parent": 22}, {"command": 9, "file": 10, "line": 137, "parent": 23}, {"command": 8, "file": 9, "line": 78, "parent": 24}, {"file": 8, "parent": 25}, {"command": 7, "file": 8, "line": 55, "parent": 26}, {"file": 7, "parent": 27}, {"command": 6, "file": 7, "line": 61, "parent": 28}, {"command": 6, "file": 7, "line": 83, "parent": 28}, {"command": 7, "file": 3, "line": 43, "parent": 11}, {"file": 14, "parent": 31}, {"command": 10, "file": 14, "line": 45, "parent": 32}, {"command": 9, "file": 10, "line": 137, "parent": 33}, {"command": 8, "file": 9, "line": 78, "parent": 34}, {"file": 13, "parent": 35}, {"command": 7, "file": 13, "line": 55, "parent": 36}, {"file": 12, "parent": 37}, {"command": 6, "file": 12, "line": 61, "parent": 38}, {"file": 1, "line": -1, "parent": 0}, {"command": 14, "file": 16, "line": 1, "parent": 40}, {"command": 13, "file": 15, "line": 861, "parent": 41}, {"command": 12, "file": 0, "line": 2124, "parent": 42}, {"command": 11, "file": 0, "line": 2199, "parent": 43}, {"command": 16, "file": 15, "line": 887, "parent": 41}, {"command": 15, "file": 0, "line": 401, "parent": 45}, {"command": 13, "file": 0, "line": 2338, "parent": 46}, {"command": 12, "file": 0, "line": 2124, "parent": 47}, {"command": 11, "file": 0, "line": 2199, "parent": 48}, {"command": 13, "file": 0, "line": 2407, "parent": 46}, {"command": 12, "file": 0, "line": 2124, "parent": 50}, {"command": 11, "file": 0, "line": 2199, "parent": 51}, {"command": 17, "file": 0, "line": 2530, "parent": 46}, {"command": 11, "file": 0, "line": 2074, "parent": 53}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 15, "fragment": "-Zc:__cplusplus"}, {"backtrace": 15, "fragment": "-permissive-"}, {"backtrace": 15, "fragment": "-utf-8"}], "defines": [{"backtrace": 15, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_GUI_LIB"}, {"backtrace": 15, "define": "QT_NO_DEBUG"}, {"backtrace": 6, "define": "QT_WIDGETS_LIB"}, {"backtrace": 15, "define": "UNICODE"}, {"backtrace": 15, "define": "WIN32"}, {"backtrace": 15, "define": "WIN64"}, {"backtrace": 15, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 15, "define": "_UNICODE"}, {"backtrace": 15, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen/include_MinSizeRel"}, {"backtrace": 6, "path": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include"}, {"backtrace": 6, "path": "C:/Program Files/Euresys/eGrabber/include"}, {"backtrace": 15, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 15, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include"}, {"backtrace": 15, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [15], "standard": "17"}, "sourceIndexes": [0, 1, 5]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 6, "id": "UserInterface::@6890427a1f51a3e7e1df"}], "id": "UserInterfaceApp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 5, "path": "bin"}], "prefix": {"path": "C:/Program Files/HVIS_UserInterface"}}, "link": {"commandFragments": [{"fragment": "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO", "role": "flags"}, {"fragment": "/subsystem:windows", "role": "flags"}, {"backtrace": 6, "fragment": "lib\\MinSizeRel\\UserInterface.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 20, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6EntryPoint.lib", "role": "libraries"}, {"backtrace": 30, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 39, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "UserInterfaceApp", "nameOnDisk": "UserInterfaceApp.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 6, 7]}, {"name": "Source Files", "sourceIndexes": [1, 5]}, {"name": "", "sourceIndexes": [2, 3, 4]}, {"name": "CMake Rules", "sourceIndexes": [8, 9, 10]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/UserInterfaceApp_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 1}, {"backtrace": 44, "path": "HexcelVisionApp_es_ES.ts", "sourceGroupIndex": 2}, {"backtrace": 49, "isGenerated": true, "path": "build/.qt/rcc/UserInterfaceApp_translations.qrc", "sourceGroupIndex": 2}, {"backtrace": 52, "isGenerated": true, "path": "build/HexcelVisionApp_es_ES.qm", "sourceGroupIndex": 2}, {"backtrace": 54, "compileGroupIndex": 0, "isGenerated": true, "path": "build/.qt/rcc/qrc_UserInterfaceApp_translations.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/UserInterfaceApp_autogen/include_MinSizeRel/ui/ui_mainwindow.h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/UserInterfaceApp_autogen/autouic_MinSizeRel.stamp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/a3ac39d91d2173075252beb9614a48a1/HexcelVisionApp_es_ES.qm.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/997dab26d2a4683c7b070ca56dc3fb1c/qrc_UserInterfaceApp_translations.cpp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/cf7f32f648246ff0cf66c5ffe7652e82/autouic_(CONFIG).stamp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}