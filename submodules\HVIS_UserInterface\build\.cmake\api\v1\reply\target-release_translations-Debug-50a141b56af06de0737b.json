{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "qt6_add_lrelease", "qt6_add_translations", "add_dependencies"], "files": ["C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake:817:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 2, "file": 1, "line": 1, "parent": 1}, {"command": 1, "file": 0, "line": 848, "parent": 2}, {"command": 0, "file": 0, "line": 618, "parent": 3}, {"command": 3, "file": 0, "line": 620, "parent": 3}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}, {"backtrace": 5, "id": "HVIS_UserInterface_lrelease::@6890427a1f51a3e7e1df"}], "id": "release_translations::@6890427a1f51a3e7e1df", "name": "release_translations", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}