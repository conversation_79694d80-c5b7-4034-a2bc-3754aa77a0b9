/**
 * @file EuresysFrameGrabber.h
 * @brief Header file for the EuresysFrameGrabber class
 * <AUTHOR> Team
 * @date 2025
 */

#ifndef EURESYS_FRAME_GRABBER_H
#define EURESYS_FRAME_GRABBER_H

// #include "FrameGrabberBase.h"
#include <memory>
#include <functional>
#include "spdlog/spdlog.h"
#include <EGrabber.h>

/**
 * @namespace HVIS
 * @brief Namespace for HVIS project components
 */
namespace HVIS
{
/**
 * @namespace Internal
 * @brief Namespace for internal implementation details
 */
namespace Internal
{
    /**
     * @brief Internal class handling low-level frame grabber operations
     * 
     */
    class EuresysFrameGrabberInternal;

    /**
     * @brief Callback function type for processing new frames
     * @param bufferData Pointer to the frame buffer data
     * @param width Width of the frame in pixels
     * @param height Height of the frame in pixels
     * @param size Size of the frame buffer in bytes
     * @param frameId Unique identifier for the frame
     */
    using CallbackFunc = std::function<void(uint8_t* bufferData, size_t width, size_t height, size_t size, size_t frameId)>;
}

/**
 * @class EuresysFrameGrabber
 * @brief Euresys-specific frame grabber implementation
 * 
 * This class provides a high-level interface for capturing images from
 * Euresys frame grabbers, including Coaxlink, Grablink, Gigelink, and Playlink.
 */
class EuresysFrameGrabber
{
public:

    /**
     * @brief Enum class for the different Euresys producers
     */
    enum class EuresysProducer
    {
        Coaxlink,  ///< Coaxlink frame grabber
        Grablink,  ///< Grablink frame grabber
        Gigelink,  ///< Gigelink frame grabber
        Playlink   ///< Playlink virtual frame grabber
    };

    /**
     * @brief Constructor
     */
    EuresysFrameGrabber();

    /**
     * @brief Destructor
     */
    ~EuresysFrameGrabber();

    /**
     * @brief Initialize the frame grabber with the specified producer
     * 
     * This method initializes the frame grabber and performs camera discovery.
     * 
     * @param t_producer The Euresys producer to use
     * @throw std::runtime_error If initialization fails
     */
    void initialize(EuresysProducer t_producer);

    /**
     * @brief Select the camera to use
     * 
     * @param cameraIndex The index of the camera to use. If SIZE_MAX, user will be prompted to select.
     * @throw std::runtime_error If no cameras are available or an invalid index is provided
     */
    void selectCamera(size_t cameraIndex = SIZE_MAX);

    /**
     * @brief Allocate buffers for the selected camera
     * 
     * @param numBuffers The number of buffers to allocate
     */
    void allocBuffers(size_t numBuffers);

    /**
     * @brief Allocate a GPU buffer
     */
    void allocateAndAnnounceGPUBuffers(size_t numBuffers);

    /**
     * @brief Start image acquisition
     */
    void startAcquisition();

    /**
     * @brief Stop image acquisition
     */
    void stopAcquisition();

    /**
     * @brief Set the callback function for processing new frames
     * 
     * @param callback The callback function
     */
    void setCallback(Internal::CallbackFunc callback);

    /**
     * @brief Check if acquisition is started
     * 
     * @return true If acquisition is started
     * @return false If acquisition is not started
     */
    bool isAcquisitionStarted() const;

    /**
     * @brief Check if the frame grabber is initialized
     * 
     * @return true If the frame grabber is initialized
     * @return false If the frame grabber is not initialized
     */
    bool isInitialized() const;

    /**
     * @brief Get the list of discovered cameras
     * 
     * @return std::vector<std::string> The list of discovered cameras
     */
    std::vector<std::string> getCameraList() const;

    /**
     * @brief Get a description list for all discovered cameras
     * 
     * @return std::vector<std::string> The list of camera descriptions
     */
    std::vector<std::string> getCameraDescriptionList() const;

    /**
     * @brief Configure Euresys devices
     */
    void configureEuresysDevices();

private:
    std::unique_ptr<Euresys::EGenTL> m_genTL;                               ///< GenTL producer
    std::unique_ptr<Euresys::EGrabberDiscovery> m_discovery;                ///< Camera discovery object
    std::unique_ptr<Internal::EuresysFrameGrabberInternal> m_frameGrabber;  ///< Internal frame grabber implementation
    std::vector<Euresys::EGrabberCameraInfo> m_cameras;                     ///< List of discovered cameras
    
    std::vector<unsigned char *> m_pinnedMemory;
    
    std::shared_ptr<spdlog::logger> m_logger;                               ///< Logger instance
};
} // namespace HVIS
#endif // EURESYS_FRAME_GRABBER_H
