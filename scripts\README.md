# HVIS Suite - Build Scripts

This directory contains the build scripts for the HVIS Suite project. The build system has been optimized to separate dependency management from compilation, allowing for faster iterative development.

## Quick Start

### First Time Setup
```bash
# Install dependencies (only needed once or when dependencies change)
scripts\setup_dependencies.bat

# Build the application
scripts\build_fast.bat app
```

### Daily Development
```bash
# Fast compilation (no Conan overhead)
scripts\build_fast.bat app
```

## Script Overview

### 🚀 `setup_dependencies.bat` - Dependency Management
**Purpose**: Install and manage project dependencies via Conan.
**When to use**: 
- First time setup
- When dependencies change in `conanfile.py`
- When you want to update/reinstall dependencies

**Usage**:
```bash
scripts\setup_dependencies.bat [options]

Options:
  --force, -f      Force reinstall all dependencies
  --debug, -d      Install debug versions of dependencies  
  --verbose, -v    Show verbose output during installation
  --help, -h       Show help message
```

**Examples**:
```bash
scripts\setup_dependencies.bat                    # Install release dependencies
scripts\setup_dependencies.bat --force           # Force reinstall all dependencies
scripts\setup_dependencies.bat --debug --verbose # Install debug deps with verbose output
```

### ⚡ `build_fast.bat` - Fast Compilation
**Purpose**: Quick compilation without Conan overhead.
**When to use**: 
- Daily development and testing
- When you've only changed source code
- When dependencies are already installed

**Usage**:
```bash
scripts\build_fast.bat [targets] [options]

Targets:
  lib              Build library (default: ON)
  app              Build application
  test, tests      Build and run tests
  doc, docs        Build documentation

Build Types:
  release          Release build (default)
  debug            Debug build

Options:
  clean            Clean build (remove build artifacts, keep dependencies)
  --verbose, -v    Show verbose build output
  --parallel N, -j N  Use N parallel jobs (default: CPU count)
  --help, -h       Show help message
```

**Examples**:
```bash
scripts\build_fast.bat app                    # Build application (release)
scripts\build_fast.bat app debug              # Build application (debug)
scripts\build_fast.bat app test               # Build app and run tests
scripts\build_fast.bat clean app --verbose    # Clean build with verbose output
scripts\build_fast.bat app -j 4               # Build with 4 parallel jobs
```

### 🔄 `build.bat` - Smart Build Launcher
**Purpose**: Automatically handles dependency setup and forwards to fast build.
**When to use**: 
- When you're unsure if dependencies are installed
- As a general-purpose build command
- For CI/CD pipelines

**Usage**:
```bash
scripts\build.bat [same arguments as build_fast.bat]
scripts\build.bat --legacy  # Use old build behavior (always runs Conan)
```

### 📦 `update_conan.bat` - Legacy Script
**Purpose**: Redirects to the new dependency management system.
**Status**: Deprecated - use `setup_dependencies.bat` instead.

## Build Workflow

### Recommended Development Workflow

1. **Initial Setup** (once):
   ```bash
   scripts\setup_dependencies.bat
   ```

2. **Daily Development** (fast iterations):
   ```bash
   scripts\build_fast.bat app
   # Make code changes
   scripts\build_fast.bat app
   # Repeat...
   ```

3. **When Dependencies Change**:
   ```bash
   scripts\setup_dependencies.bat --force
   scripts\build_fast.bat app
   ```

4. **Clean Build** (when things go wrong):
   ```bash
   scripts\build_fast.bat clean app
   ```

### IDE Integration

You can configure your IDE to use these scripts:

**Visual Studio Code**:
- Build Task: `scripts\build_fast.bat app`
- Clean Task: `scripts\build_fast.bat clean app`

**Visual Studio**:
- Pre-build: `scripts\setup_dependencies.bat` (only if needed)
- Build: Use CMake integration with `build\conan_toolchain.cmake`

## File Structure

After running `setup_dependencies.bat`, the following files are created in the `build` directory:

```
build/
├── conan_toolchain.cmake      # CMake toolchain for dependencies
├── cmakedeps_macros.cmake     # CMake dependency macros
├── *Config.cmake              # Package configuration files
├── conanbuild.bat             # Conan build environment
├── conanrun.bat               # Conan runtime environment
└── conan_install.log          # Installation log (if not verbose)
```

## Troubleshooting

### Common Issues

1. **"Dependencies not found" error**:
   ```bash
   scripts\setup_dependencies.bat
   ```

2. **Build fails with dependency errors**:
   ```bash
   scripts\setup_dependencies.bat --force
   ```

3. **Conan profile issues**:
   ```bash
   conan profile detect --force
   scripts\setup_dependencies.bat --force
   ```

4. **Build artifacts corruption**:
   ```bash
   scripts\build_fast.bat clean app
   ```

5. **Complete reset**:
   ```bash
   rmdir /s /q build
   scripts\setup_dependencies.bat
   scripts\build_fast.bat app
   ```

### Performance Tips

- Use `build_fast.bat` for daily development (skips Conan)
- Use parallel builds: `build_fast.bat app -j 8`
- Only run `setup_dependencies.bat` when needed
- Use `clean` option sparingly (preserves dependencies)

### Verbose Output

For debugging build issues, use the `--verbose` flag:
```bash
scripts\setup_dependencies.bat --verbose
scripts\build_fast.bat app --verbose
```

## Migration from Old Build System

If you were using the old `build.bat` script:

**Old way**:
```bash
scripts\build.bat app  # Always runs Conan install
```

**New way**:
```bash
scripts\setup_dependencies.bat  # Once
scripts\build_fast.bat app       # Fast iterations
```

**Legacy support**:
```bash
scripts\build.bat --legacy app   # Uses old behavior
```

## Benefits of New System

1. **Faster Builds**: Dependency installation separated from compilation
2. **Better Caching**: Dependencies cached until explicitly updated
3. **Parallel Builds**: Automatic CPU core detection and parallel compilation
4. **Better Error Handling**: Detailed error messages and troubleshooting tips
5. **Flexible Options**: Debug/release, verbose output, clean builds
6. **IDE Friendly**: Separate scripts for different IDE integration needs
