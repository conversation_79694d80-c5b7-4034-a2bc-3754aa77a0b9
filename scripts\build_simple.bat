@echo off
setlocal enabledelayedexpansion

echo ========================================
echo      HVIS Suite - Simple Build        
echo ========================================
echo.

:: Parse command line arguments
set BUILD_LIB=ON
set BUILD_APP=OFF
set BUILD_TESTS=OFF
set BUILD_DOCS=OFF
set BUILD_TYPE=Release
set PARALLEL_JOBS=%NUMBER_OF_PROCESSORS%
set VERBOSE=OFF
set CLEAN_BUILD=OFF

:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="lib" set BUILD_LIB=ON
if /i "%~1"=="app" set BUILD_APP=ON
if /i "%~1"=="test" set BUILD_TESTS=ON
if /i "%~1"=="tests" set BUILD_TESTS=ON
if /i "%~1"=="docs" set BUILD_DOCS=ON
if /i "%~1"=="doc" set BUILD_DOCS=ON
if /i "%~1"=="debug" set BUILD_TYPE=Debug
if /i "%~1"=="release" set BUILD_TYPE=Release
if /i "%~1"=="clean" set CLEAN_BUILD=ON
if /i "%~1"=="--verbose" set VERBOSE=ON
if /i "%~1"=="-v" set VERBOSE=ON
if /i "%~1"=="--help" goto :show_help
if /i "%~1"=="-h" goto :show_help
shift
goto :parse_args

:done_parsing

echo Build Configuration:
echo   Library: %BUILD_LIB%
echo   Application: %BUILD_APP%
echo   Tests: %BUILD_TESTS%
echo   Documentation: %BUILD_DOCS%
echo   Build Type: %BUILD_TYPE%
echo   Parallel Jobs: %PARALLEL_JOBS%
echo   Clean Build: %CLEAN_BUILD%
echo.

:: Change to project root directory
cd /d "%~dp0\.."

:: Check if dependencies are installed
if not exist "build\conan_toolchain.cmake" (
    echo Error: Dependencies not found!
    echo.
    echo Please run setup_dependencies.bat first:
    echo   scripts\setup_dependencies.bat
    echo.
    echo This will install all required dependencies via Conan.
    pause
    exit /b 1
)

:: Clean build if requested
if "%CLEAN_BUILD%"=="ON" (
    echo Cleaning build directory (preserving dependencies)...
    cd build
    if exist CMakeCache.txt del CMakeCache.txt
    if exist CMakeFiles rmdir /s /q CMakeFiles
    if exist HVIS_Suite.dir rmdir /s /q HVIS_Suite.dir
    if exist HVIS_Suite_autogen rmdir /s /q HVIS_Suite_autogen
    if exist *.vcxproj del *.vcxproj
    if exist *.vcxproj.filters del *.vcxproj.filters
    if exist *.sln del *.sln
    if exist bin rmdir /s /q bin
    if exist lib rmdir /s /q lib
    if exist x64 rmdir /s /q x64
    cd ..
    echo Build directory cleaned.
    echo.
)

:: Create build directory if it doesn't exist
if not exist build mkdir build
cd build

:: Configure with CMake (only if needed or if clean build)
set NEED_CONFIGURE=OFF
if not exist "CMakeCache.txt" set NEED_CONFIGURE=ON
if "%CLEAN_BUILD%"=="ON" set NEED_CONFIGURE=ON

if "%NEED_CONFIGURE%"=="ON" (
    echo Configuring with CMake...
    cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake -DBUILD_APP=%BUILD_APP% -DBUILD_TESTS=%BUILD_TESTS% -DBUILD_DOCS=%BUILD_DOCS%
    
    if %ERRORLEVEL% neq 0 (
        echo CMake configuration failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
    echo CMake configuration completed.
) else (
    echo Skipping CMake configuration (already configured).
)
echo.

:: Build the project
echo Building project with %PARALLEL_JOBS% parallel jobs...

cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS%

if %ERRORLEVEL% neq 0 (
    echo Build failed with error code %ERRORLEVEL%
    echo.
    echo Troubleshooting tips:
    echo - Try a clean build: build_simple.bat clean app
    echo - Check if dependencies are up to date: setup_dependencies.bat --force
    pause
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!
echo.

:: Run tests if they were built
if "%BUILD_TESTS%"=="ON" (
    echo Running tests...
    ctest -C %BUILD_TYPE% --output-on-failure
    if %ERRORLEVEL% neq 0 (
        echo Tests failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
    echo All tests passed!
    echo.
)

:: Build documentation if requested
if "%BUILD_DOCS%"=="ON" (
    echo Building documentation...
    cmake --build . --target docs
    if %ERRORLEVEL% neq 0 (
        echo Documentation generation failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
    echo Documentation generated successfully!
    echo.
)

:: Return to original directory
cd ..

:: Display results
echo ========================================
echo        Build Completed Successfully!   
echo ========================================
echo.

if "%BUILD_APP%"=="ON" (
    if exist "build\bin\%BUILD_TYPE%\HVIS_Suite.exe" (
        echo Application built: build\bin\%BUILD_TYPE%\HVIS_Suite.exe
    ) else (
        echo Application location: build\bin\%BUILD_TYPE%\
    )
)

if "%BUILD_TESTS%"=="ON" (
    echo Test executables: build\bin\%BUILD_TYPE%\
)

if "%BUILD_DOCS%"=="ON" (
    echo Documentation: build\docs\html\index.html
)

echo.
pause
goto :eof

:show_help
echo.
echo HVIS Suite - Simple Build Script
echo.
echo Usage:
echo   build_simple.bat [targets] [options]
echo.
echo Targets:
echo   lib              Build library (default: ON)
echo   app              Build application
echo   test, tests      Build and run tests
echo   doc, docs        Build documentation
echo.
echo Build Types:
echo   release          Release build (default)
echo   debug            Debug build
echo.
echo Options:
echo   clean            Clean build (remove build artifacts, keep dependencies)
echo   --verbose, -v    Show verbose build output
echo   --help, -h       Show this help message
echo.
echo Examples:
echo   build_simple.bat app                    # Build application (release)
echo   build_simple.bat app debug              # Build application (debug)
echo   build_simple.bat app test               # Build app and run tests
echo   build_simple.bat clean app              # Clean build
echo.
echo Note:
echo This script assumes dependencies are already installed.
echo If you get dependency errors, run: scripts\setup_dependencies.bat
echo.
pause
goto :eof
