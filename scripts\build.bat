@echo off
setlocal enabledelayedexpansion

:: Parse command line arguments
set BUILD_LIB=ON
set BUILD_APP=OFF
set BUILD_TESTS=OFF
set BUILD_DOCS=OFF
set BUILD_TYPE=Release

:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="lib" set BUILD_LIB=ON
if /i "%~1"=="app" set BUILD_APP=ON
if /i "%~1"=="test" set BUILD_TESTS=ON
if /i "%~1"=="tests" set BUILD_TESTS=ON
if /i "%~1"=="docs" set BUILD_DOCS=ON
if /i "%~1"=="doc" set BUILD_DOCS=ON
if /i "%~1"=="debug" set BUILD_TYPE=Debug
if /i "%~1"=="release" set BUILD_TYPE=Release
shift
goto :parse_args

:done_parsing

echo Building HVIS_ImageAcquisition...
echo Build configuration:
echo   Library: %BUILD_LIB%
echo   Application: %BUILD_APP%
echo   Tests: %BUILD_TESTS%
echo   Documentation: %BUILD_DOCS%
echo   Build type: %BUILD_TYPE%

:: If "clean" is passed as an argument, delete the build directory
if /i "%~1"=="clean" (
    echo Cleaning build directory...
    if exist build rmdir /s /q build
    goto :eof
)

:: Create build directory if it doesn't exist
cd ..
if not exist build mkdir build
cd build

:: Run Conan to install dependencies
echo Installing dependencies with Conan...
conan install .. --output-folder=. --build=missing
if %ERRORLEVEL% neq 0 (
    echo Conan installation failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

:: Configure with CMake
echo Configuring with CMake...
cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake -DBUILD_APP=%BUILD_APP% -DBUILD_TESTS=%BUILD_TESTS% -DBUILD_DOCS=%BUILD_DOCS%
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

:: Build the project
echo Building project...
cmake --build . --config %BUILD_TYPE%
if %ERRORLEVEL% neq 0 (
    echo Build failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

:: Run tests if they were built
if "%BUILD_TESTS%"=="ON" (
    echo Running tests...
    ctest -C %BUILD_TYPE% --output-on-failure
    if %ERRORLEVEL% neq 0 (
        echo Tests failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
)

:: Build documentation if requested
if "%BUILD_DOCS%"=="ON" (
    echo Building documentation...
    cmake --build . --target docs
    if %ERRORLEVEL% neq 0 (
        echo Documentation generation failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
    echo Documentation generated in build\docs\html
)

:: Return to original directory
cd ..

echo Build completed successfully.
if "%BUILD_APP%"=="ON" echo Application is in build\bin\%BUILD_TYPE%\HVIS_Suite.exe
if "%BUILD_DOCS%"=="ON" echo Documentation is in build\docs\html\index.html
pause