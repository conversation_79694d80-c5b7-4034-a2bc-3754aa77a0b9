@echo off
setlocal enabledelayedexpansion
call :setESC

:: Check if this is a request for the old build behavior
set OLD_BUILD=OFF
for %%a in (%*) do (
    if /i "%%a"=="--legacy" set OLD_BUILD=ON
    if /i "%%a"=="--old" set OLD_BUILD=ON
)

if "%OLD_BUILD%"=="ON" goto :legacy_build

echo %ESC%[96m========================================%ESC%[0m
echo %ESC%[96m        HVIS Suite - Build Launcher     %ESC%[0m
echo %ESC%[96m========================================%ESC%[0m
echo.

:: Check if dependencies are installed
if not exist "build\conan_toolchain.cmake" (
    echo %ESC%[93mDependencies not found. Setting up dependencies first...%ESC%[0m
    echo.
    call "%~dp0setup_dependencies.bat"
    if %ERRORLEVEL% neq 0 (
        echo %ESC%[91mDependency setup failed.%ESC%[0m
        pause
        exit /b %ERRORLEVEL%
    )
    echo.
    echo %ESC%[92mDependencies setup completed. Now building...%ESC%[0m
    echo.
)

:: Forward all arguments to the fast build script
call "%~dp0build_fast.bat" %*
exit /b %ERRORLEVEL%

:legacy_build
echo %ESC%[93mUsing legacy build process (includes Conan install)...%ESC%[0m
echo.

:: Legacy build process (original build.bat functionality)
set BUILD_LIB=ON
set BUILD_APP=OFF
set BUILD_TESTS=OFF
set BUILD_DOCS=OFF
set BUILD_TYPE=Release

:parse_legacy_args
if "%~1"=="" goto :done_legacy_parsing
if /i "%~1"=="lib" set BUILD_LIB=ON
if /i "%~1"=="app" set BUILD_APP=ON
if /i "%~1"=="test" set BUILD_TESTS=ON
if /i "%~1"=="tests" set BUILD_TESTS=ON
if /i "%~1"=="docs" set BUILD_DOCS=ON
if /i "%~1"=="doc" set BUILD_DOCS=ON
if /i "%~1"=="debug" set BUILD_TYPE=Debug
if /i "%~1"=="release" set BUILD_TYPE=Release
if /i "%~1"=="clean" (
    echo Cleaning build directory...
    cd /d "%~dp0\.."
    if exist build rmdir /s /q build
    goto :eof
)
shift
goto :parse_legacy_args

:done_legacy_parsing

echo Building HVIS_ImageAcquisition...
echo Build configuration:
echo   Library: %BUILD_LIB%
echo   Application: %BUILD_APP%
echo   Tests: %BUILD_TESTS%
echo   Documentation: %BUILD_DOCS%
echo   Build type: %BUILD_TYPE%

:: Create build directory if it doesn't exist
cd /d "%~dp0\.."
if not exist build mkdir build
cd build

:: Run Conan to install dependencies
echo Installing dependencies with Conan...
conan install .. --output-folder=. --build=missing
if %ERRORLEVEL% neq 0 (
    echo Conan installation failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

:: Configure with CMake
echo Configuring with CMake...
cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake -DBUILD_APP=%BUILD_APP% -DBUILD_TESTS=%BUILD_TESTS% -DBUILD_DOCS=%BUILD_DOCS%
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

:: Build the project
echo Building project...
cmake --build . --config %BUILD_TYPE%
if %ERRORLEVEL% neq 0 (
    echo %ESC%[91mBuild failed with error code %ERRORLEVEL%%ESC%[0m
    pause
    exit /b %ERRORLEVEL%
)

:: Run tests if they were built
if "%BUILD_TESTS%"=="ON" (
    echo Running tests...
    ctest -C %BUILD_TYPE% --output-on-failure
    if %ERRORLEVEL% neq 0 (
        echo Tests failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
)

:: Build documentation if requested
if "%BUILD_DOCS%"=="ON" (
    echo Building documentation...
    cmake --build . --target docs
    if %ERRORLEVEL% neq 0 (
        echo Documentation generation failed with error code %ERRORLEVEL%
        pause
        exit /b %ERRORLEVEL%
    )
    echo Documentation generated in build\docs\html
)

:: Return to original directory
cd ..

echo %ESC%[92mBuild completed successfully.%ESC%[0m
if "%BUILD_APP%"=="ON" echo Application is in build\bin\%BUILD_TYPE%\HVIS_Suite.exe
if "%BUILD_DOCS%"=="ON" echo Documentation is in build\docs\html\index.html
pause
goto :eof

:: Function to set ESC sequence
:setESC
for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
  set ESC=%%b
  exit /B 0
)
exit /b 0