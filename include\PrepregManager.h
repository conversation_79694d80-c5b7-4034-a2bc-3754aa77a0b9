#pragma once
#include "EuresysFrameGrabber.h"
#include "mainwindow.h"

#include <memory>
#include <string>
#include <QObject>

class PrepregManager : public QObject
{
    Q_OBJECT

public:
    PrepregManager(int argc, char** argv);
    ~PrepregManager();

    int show();
    void runImageAcquisition();
    
private slots:
    void doSomethingOnTheInterface();

private:
    std::unique_ptr<HVIS::EuresysFrameGrabber> m_frameGrabber;
    std::unique_ptr<MainWindow> m_uiManager;
    QApplication* m_app;
};