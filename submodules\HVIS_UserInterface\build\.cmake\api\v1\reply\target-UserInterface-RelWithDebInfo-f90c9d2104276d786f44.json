{"artifacts": [{"path": "bin/RelWithDebInfo/UserInterface.dll"}, {"path": "lib/RelWithDebInfo/UserInterface.lib"}, {"path": "bin/RelWithDebInfo/UserInterface.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 55, "parent": 0}, {"command": 4, "file": 0, "line": 24, "parent": 0}, {"file": 3, "parent": 3}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 2, "parent": 5}, {"command": 3, "file": 2, "line": 55, "parent": 6}, {"file": 1, "parent": 7}, {"command": 2, "file": 1, "line": 61, "parent": 8}, {"command": 4, "file": 3, "line": 212, "parent": 4}, {"file": 5, "parent": 10}, {"command": 3, "file": 5, "line": 57, "parent": 11}, {"file": 4, "parent": 12}, {"command": 2, "file": 4, "line": 61, "parent": 13}, {"command": 3, "file": 2, "line": 43, "parent": 6}, {"file": 10, "parent": 15}, {"command": 6, "file": 10, "line": 45, "parent": 16}, {"command": 5, "file": 9, "line": 137, "parent": 17}, {"command": 4, "file": 8, "line": 78, "parent": 18}, {"file": 7, "parent": 19}, {"command": 3, "file": 7, "line": 55, "parent": 20}, {"file": 6, "parent": 21}, {"command": 2, "file": 6, "line": 61, "parent": 22}, {"command": 7, "file": 0, "line": 52, "parent": 0}, {"command": 8, "file": 0, "line": 61, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 2, "fragment": "-Zc:__cplusplus"}, {"backtrace": 2, "fragment": "-permissive-"}, {"backtrace": 2, "fragment": "-utf-8"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NO_DEBUG"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 24, "define": "USERINTERFACE_LIBRARY"}, {"define": "UserInterface_EXPORTS"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/include_RelWithDebInfo"}, {"backtrace": 25, "path": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include"}, {"backtrace": 25, "path": "C:/Program Files/Euresys/eGrabber/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "C:/Qt_6/6.9.0/msvc2022_64/include/QtGui"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "UserInterface::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"fragment": "/DEF:UserInterface.dir\\RelWithDebInfo\\exports.def", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Qt_6\\6.9.0\\msvc2022_64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "d3d12.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "UserInterface", "nameOnDisk": "UserInterface.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0, 5, 6]}, {"name": "Source Files", "sourceIndexes": [1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 3]}, {"name": "", "sourceIndexes": [4]}, {"name": "CMake Rules", "sourceIndexes": [7]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/UserInterface_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/mainwindow.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "build/UserInterface_autogen/include_Release/ui_mainwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "include/mainwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "ui/mainwindow.ui", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/UserInterface_autogen/include_RelWithDebInfo/ui/ui_mainwindow.h", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/UserInterface_autogen/autouic_RelWithDebInfo.stamp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/0c926193afbfc73729bd24d8ef5ea95a/autouic_(CONFIG).stamp.rule", "sourceGroupIndex": 4}], "type": "SHARED_LIBRARY"}