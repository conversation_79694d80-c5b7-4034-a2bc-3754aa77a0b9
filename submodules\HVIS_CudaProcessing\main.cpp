#include <iostream>
#include <tuple>
#include <functional>
#include "ImageCropping.cuh"
#include "ImageOperations.h"

#include "benchmark.h"


int main()
{
    // Load the image
    cv::Mat image = cv::imread("C:/Users/<USER>/repos/HVIS_CudaProcessing/test_images/test8.1.jpeg");
    if (image.empty()) {
        std::cerr << "Failed to load image!" << std::endl;
        return -1;
    }

    // // Move the image to the GPU
    cv::cuda::GpuMat d_image;
    d_image.upload(image);
    uint8_t* gpuPtr = d_image.ptr();

    // Use the pointer-based function (keeping your original signature)
    benchmarkFunctionWithTiming(splitImageCUDA, 1000, gpuPtr, d_image.cols, d_image.rows, d_image.channels(), d_image.type(), d_image.step, 640, 640);

    return 0;
}


