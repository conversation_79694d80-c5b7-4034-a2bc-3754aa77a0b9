#include <iostream>
#include "ImageCropping.cuh"


int main() {
    // Test data
    int inputWidth = 1024;
    int inputHeight = 1024;
    int regionWidth = 256;
    int regionHeight = 256;
    int numCols = 4;
    
    // Allocate host and device memory
    size_t inputSize = inputWidth * inputHeight;
    size_t outputSize = regionWidth * regionHeight * numCols;
    
    unsigned char* h_input = new unsigned char[inputSize];
    unsigned char* h_output = new unsigned char[outputSize];
    
    unsigned char* d_input;
    unsigned char* d_output;
    
    cudaMalloc(&d_input, inputSize);
    cudaMalloc(&d_output, outputSize);
    
    // Initialize input data (for testing, just set to sequential values)
    for (int i = 0; i < inputSize; i++) {
        h_input[i] = i % 256;
    }
    
    // Copy input data to device
    cudaMemcpy(d_input, h_input, inputSize, cudaMemcpyHostToDevice);
    
    // Launch kernel
    launchCropKernel(d_input, d_output, inputWidth, inputHeight, regionWidth, regionHeight, numCols);
    
    // Copy output data back to host
    cudaMemcpy(h_output, d_output, outputSize, cudaMemcpyDeviceToHost);
    
    // Verify output (for testing, just print first few values)
    for (int i = 0; i < 10; i++) {
        std::cout << static_cast<int>(h_output[i]) << " ";
    }
    std::cout << std::endl;
    
    // Clean up
    delete[] h_input;
    delete[] h_output;
    cudaFree(d_input);
    cudaFree(d_output);
    
    return 0;
}


