#include "EuresysFrameGrabber.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>

#ifdef _WIN32
    #include <direct.h>
#else
  #include <unistd.h>
  #include <sys/stat.h>
#endif

#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/rotating_file_sink.h"


// Platform-specific directory creation
bool createDirectory(const std::string& path) {
#ifdef _WIN32
    return _mkdir(path.c_str()) == 0 || errno == EEXIST;
#else
    return mkdir(path.c_str(), 0755) == 0 || errno == EEXIST;
#endif
}

// Simple callback function to process frames
void processFrame(void* bufferData, size_t width, size_t height, size_t size, size_t frameId)
{
    auto logger = spdlog::get("main");
    logger->debug("Frame received: ID={}, Size={}, Resolution={}x{}", frameId, size, width, height);
    
    // Here you would typically process the image data
    // For example: display, save, or analyze the frame
}

int main()
{
    try
    {
        // Create log directory if it doesn't exist
        createDirectory("logs");
        
        // TODO: move all the logger declarations to another file at global scope called Loggers.h
        // Initialize loggers
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>("logs/image_acquisition.log", 1024*1024*5, 3);
        
        // Configure sinks
        console_sink->set_level(spdlog::level::debug);
        file_sink->set_level(spdlog::level::debug);
        
        // Create loggers with multiple sinks
        std::vector<spdlog::sink_ptr> sinks {console_sink, file_sink};
        
        auto main_logger = std::make_shared<spdlog::logger>("main", sinks.begin(), sinks.end());
        auto euresys_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabber", sinks.begin(), sinks.end());
        auto internal_logger = std::make_shared<spdlog::logger>("EuresysFrameGrabberInternal", sinks.begin(), sinks.end());
        
        // Register loggers
        spdlog::register_logger(main_logger);
        spdlog::register_logger(euresys_logger);
        spdlog::register_logger(internal_logger);
        
        // Set log levels
        main_logger->set_level(spdlog::level::debug);
        euresys_logger->set_level(spdlog::level::debug);
        internal_logger->set_level(spdlog::level::debug);
        
        main_logger->info("Starting image acquisition demo...");

        // Create a playlink grabber
        std::unique_ptr<HVIS::EuresysFrameGrabber> grabber = std::make_unique<HVIS::EuresysFrameGrabber>();
        
        // Initialize
        grabber->initialize(HVIS::EuresysFrameGrabber::EuresysProducer::Playlink);
        
        // Select camera
        grabber->selectCamera(1);

        // Set callback for frame processing
        grabber->setCallback(processFrame);
        
        // Allocate buffers
        grabber->allocBuffers(10);
        
        // Start acquisition
        main_logger->debug("Starting acquisition...");
        grabber->startAcquisition();
        
        // Run for 5 seconds
        main_logger->debug("Acquiring frames for 5 seconds...");
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // Stop acquisition
        main_logger->debug("Stopping acquisition...");
        grabber->stopAcquisition();
        
        main_logger->debug("Demo completed successfully.");
    }
    catch (const std::exception& e)
    {
        auto logger = spdlog::get("main");
        if (logger) {
            logger->error("Error: {}", e.what());
        } else {
            std::cerr << "Error: " << e.what() << std::endl;
        }
        return 1;
    }

    return 0;
}
