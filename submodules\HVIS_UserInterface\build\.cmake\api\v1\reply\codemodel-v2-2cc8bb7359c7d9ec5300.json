{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-ea0936326622c7aca78a.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "HVIS_UserInterface", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-cbfa1588a6fd90578aa0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lrelease::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lrelease-Debug-79db6d2f21bd49935f96.json", "name": "HVIS_UserInterface_lrelease", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lupdate::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lupdate-Debug-393c9cae5f3788c46628.json", "name": "HVIS_UserInterface_lupdate", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterface-Debug-fb2963eebec01a00b57f.json", "name": "UserInterface", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterfaceApp::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterfaceApp-Debug-2f71240c86284469a0bf.json", "name": "UserInterfaceApp", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-1468daf52a19f138a3c1.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "release_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-release_translations-Debug-50a141b56af06de0737b.json", "name": "release_translations", "projectIndex": 0}, {"directoryIndex": 0, "id": "update_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-update_translations-Debug-997c37369cf64d1c4533.json", "name": "update_translations", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-99376eb0a0100ba3b428.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "HVIS_UserInterface", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-cbfa1588a6fd90578aa0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lrelease::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lrelease-Release-79db6d2f21bd49935f96.json", "name": "HVIS_UserInterface_lrelease", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lupdate::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lupdate-Release-393c9cae5f3788c46628.json", "name": "HVIS_UserInterface_lupdate", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterface-Release-df69af6bbf7705a4b817.json", "name": "UserInterface", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterfaceApp::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterfaceApp-Release-865e807cb3c6a1c6fce2.json", "name": "UserInterfaceApp", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-1468daf52a19f138a3c1.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "release_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-release_translations-Release-50a141b56af06de0737b.json", "name": "release_translations", "projectIndex": 0}, {"directoryIndex": 0, "id": "update_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-update_translations-Release-997c37369cf64d1c4533.json", "name": "update_translations", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-4f804095e463a4ec9c3c.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0], "name": "HVIS_UserInterface", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-cbfa1588a6fd90578aa0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lrelease::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lrelease-MinSizeRel-79db6d2f21bd49935f96.json", "name": "HVIS_UserInterface_lrelease", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lupdate::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lupdate-MinSizeRel-393c9cae5f3788c46628.json", "name": "HVIS_UserInterface_lupdate", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterface-MinSizeRel-a145a45afd26cc928bcb.json", "name": "UserInterface", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterfaceApp::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterfaceApp-MinSizeRel-6f2147a3b5a97b6b3926.json", "name": "UserInterfaceApp", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-1468daf52a19f138a3c1.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "release_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-release_translations-MinSizeRel-50a141b56af06de0737b.json", "name": "release_translations", "projectIndex": 0}, {"directoryIndex": 0, "id": "update_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-update_translations-MinSizeRel-997c37369cf64d1c4533.json", "name": "update_translations", "projectIndex": 0}]}, {"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-f18c98c6c62955335a0f.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "HVIS_UserInterface", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-cbfa1588a6fd90578aa0.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lrelease::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lrelease-RelWithDebInfo-79db6d2f21bd49935f96.json", "name": "HVIS_UserInterface_lrelease", "projectIndex": 0}, {"directoryIndex": 0, "id": "HVIS_UserInterface_lupdate::@6890427a1f51a3e7e1df", "jsonFile": "target-HVIS_UserInterface_lupdate-RelWithDebInfo-393c9cae5f3788c46628.json", "name": "HVIS_UserInterface_lupdate", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterface::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterface-RelWithDebInfo-f90c9d2104276d786f44.json", "name": "UserInterface", "projectIndex": 0}, {"directoryIndex": 0, "id": "UserInterfaceApp::@6890427a1f51a3e7e1df", "jsonFile": "target-UserInterfaceApp-RelWithDebInfo-34400cb2f3ee014aa3e1.json", "name": "UserInterfaceApp", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-1468daf52a19f138a3c1.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "release_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-release_translations-RelWithDebInfo-50a141b56af06de0737b.json", "name": "release_translations", "projectIndex": 0}, {"directoryIndex": 0, "id": "update_translations::@6890427a1f51a3e7e1df", "jsonFile": "target-update_translations-RelWithDebInfo-997c37369cf64d1c4533.json", "name": "update_translations", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build", "source": "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface"}, "version": {"major": 2, "minor": 8}}