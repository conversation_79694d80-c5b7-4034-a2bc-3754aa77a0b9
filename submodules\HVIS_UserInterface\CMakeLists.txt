cmake_minimum_required(VERSION 3.16)
project(HVIS_UserInterface LANGUAGES CXX)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

# Set C++ standard
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Qt setup
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets LinguistTools REQUIRED)

# Find packages using Conan
find_package(spdlog REQUIRED)
message(STATUS "Found spdlog: ${spdlog_VERSION}")

# Find GTest package
find_package(GTest REQUIRED)
message(STATUS "Found GTest: ${GTest_VERSION}")

# Automatically find all source and header files
file(GLOB_RECURSE SOURCES CONFIGURE_DEPENDS "src/*.cpp")
file(GLOB_RECURSE HEADERS CONFIGURE_DEPENDS "include/*.h" "*.h")
file(GLOB_RECURSE UI_FILES CONFIGURE_DEPENDS "ui/*.ui")

# Define the library
add_library(UserInterface SHARED
  ${SOURCES}
  ${HEADERS}
  ${UI_FILES}
)

# Set UI search path for AUTOUIC
set_target_properties(UserInterface PROPERTIES
    AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/ui
    WINDOWS_EXPORT_ALL_SYMBOLS ON
)

target_compile_definitions(UserInterface PRIVATE USERINTERFACE_LIBRARY)

# Link against spdlog
target_link_libraries(UserInterface PUBLIC
    Qt6::Core
    Qt6::Widgets
    spdlog::spdlog
)

target_include_directories(UserInterface PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    "C:/Program Files/Euresys/eGrabber/include"
)

# Build options - default to building just the library
option(BUILD_APP "Build the application" ON)
option(BUILD_TESTS "Build the tests" OFF)
option(BUILD_DOCS "Build documentation" OFF)

# Add the executable if requested
if(BUILD_APP)
    qt_standard_project_setup()

    qt_add_executable(UserInterfaceApp
        WIN32 MACOSX_BUNDLE
        main.cpp
    )

    qt_add_translations(
        TARGETS UserInterfaceApp
        TS_FILES HexcelVisionApp_es_ES.ts
    )

    target_link_libraries(UserInterfaceApp PRIVATE
        UserInterface
    )

    include(GNUInstallDirs)
    install(TARGETS UserInterfaceApp
        BUNDLE  DESTINATION .
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    )

    qt_generate_deploy_app_script(
        TARGET UserInterfaceApp
        OUTPUT_SCRIPT deploy_script
        NO_UNSUPPORTED_PLATFORM_ERROR
    )
    install(SCRIPT ${deploy_script})
endif()

# Add tests if requested
if(BUILD_TESTS)
  # Enable testing
  enable_testing()
  
  # Add tests directory
  add_subdirectory(tests)
endif()

# Add Doxygen documentation if requested
if(BUILD_DOCS)
  # Find Doxygen package
  find_package(Doxygen REQUIRED)
  
  # Set Doxygen configuration variables
  set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs)
  set(DOXYGEN_PROJECT_NAME "HVIS Image Acquisition")
  set(DOXYGEN_PROJECT_BRIEF "Image acquisition library for HVIS project")
  set(DOXYGEN_EXTRACT_ALL YES)
  set(DOXYGEN_EXTRACT_PRIVATE YES)
  set(DOXYGEN_EXTRACT_PACKAGE YES)
  set(DOXYGEN_EXTRACT_STATIC YES)
  set(DOXYGEN_EXTRACT_LOCAL_CLASSES YES)
  set(DOXYGEN_RECURSIVE YES)
  set(DOXYGEN_USE_MDFILE_AS_MAINPAGE README.md)
  set(DOXYGEN_GENERATE_HTML YES)
  set(DOXYGEN_GENERATE_LATEX NO)
  
  # Add Doxygen target
  doxygen_add_docs(docs
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/README.md
    COMMENT "Generating API documentation with Doxygen"
  )
endif()
