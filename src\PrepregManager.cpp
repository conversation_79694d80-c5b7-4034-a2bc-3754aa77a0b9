/**
 * @file PrepregManager.cpp
 * @brief Implementation of the PrepregManager class - main application controller
 *
 * This file contains the implementation of the PrepregManager class which serves as
 * the central coordinator for the HVIS Suite prepreg inspection system. It manages
 * the integration between the user interface and image acquisition subsystems.
 */

#include "PrepregManager.h"

#include "ImageOperations.h"

#include <iostream>
#include <QObject>
#include <QApplication>

/**
 * @brief Constructor for PrepregManager
 * @param argc Command line argument count
 * @param argv Command line argument values
 *
 * Initializes the main application components:
 * - Creates the QApplication instance for GUI management
 * - Initializes the Euresys frame grabber for camera operations
 * - Sets up the main window UI manager
 * - Connects UI signals to appropriate slots for event handling
 */
PrepregManager::PrepregManager(int argc, char** argv)
    : m_frameGrabber(std::make_unique<HVIS::EuresysFrameGrabber>())
{
    // Initialize Qt application framework
    // m_app = new QApplication(argc, argv);

    // Create the main window UI manager
    // m_uiManager = std::make_unique<MainWindow>();

    // Connect UI action signals to the corresponding slot for handling user interactions
    // QObject::connect(m_uiManager.get(), SIGNAL(actionTriggered()), this, SLOT(doSomethingOnTheInterface()));
}

/**
 * @brief Destructor for PrepregManager
 *
 * Cleans up allocated resources, specifically the QApplication instance.
 * Note: Smart pointers (unique_ptr) automatically handle cleanup for
 * m_frameGrabber and m_uiManager.
 */
PrepregManager::~PrepregManager()
{
    delete m_app;
}

/**
 * @brief Display the main window and start the application event loop
 * @return Exit code from the Qt application event loop
 *
 * Shows the main window to the user and starts the Qt application's
 * main event loop. This function blocks until the application is closed.
 */
int PrepregManager::show()
{
    // Display the main window
    m_uiManager->show();

    // Start the Qt event loop and return the application exit code
    return m_app->exec();
}

/**
 * @brief Initialize and start the image acquisition process
 *
 * Configures the Euresys frame grabber for camera operations and starts
 * continuous image acquisition. This includes:
 * - Initializing the frame grabber with Playlink producer
 * - Selecting the first available camera (index 0)
 * - Setting up a callback function to handle incoming frames
 * - Allocating frame buffers for efficient image capture
 * - Starting the acquisition process
 */
void PrepregManager::runImageAcquisition()
{
    // Initialize the frame grabber with Playlink producer for camera communication
    m_frameGrabber->initialize(HVIS::EuresysFrameGrabber::EuresysProducer::Playlink);

    // Select the first camera (index 0) for image acquisition
    m_frameGrabber->selectCamera(0);

    // Set up callback function to handle each captured frame
    // Lambda function receives frame data and metadata for processing
    m_frameGrabber->setCallback([](void* bufferData, size_t width, size_t height, size_t size, size_t frameId) {
        std::cout << "Frame received: ID=" << frameId << ", Size=" << size << ", Resolution=" << width << "x" << height << std::endl;

        splitImageCUDA(static_cast<uint8_t*>(bufferData), width, height, 3, 16, width * 3, 640, 640);

        std::cout << "1 frame processed" << std::endl;
        exit(0);
    });

    // Allocate 10 frame buffers for efficient continuous acquisition
    // m_frameGrabber->allocBuffers(10);
    m_frameGrabber->allocateAndAnnounceGPUBuffers(10);

    // Start the image acquisition process
    m_frameGrabber->startAcquisition();

    std::this_thread::sleep_for(std::chrono::seconds(5));

    m_frameGrabber->stopAcquisition();
}

/**
 * @brief Slot function to handle UI action triggers
 *
 * This slot is connected to the UI's actionTriggered signal and serves as
 * the main event handler for user interface interactions. Currently, it
 * responds to UI actions by initiating the image acquisition process.
 *
 * @note This is a Qt slot function that gets called when the UI emits
 *       the actionTriggered signal.
 */
void PrepregManager::doSomethingOnTheInterface()
{
    // Log the UI action trigger for debugging purposes
    // std::cout << "Action triggered from the interface!" << std::endl;

    // Initiate image acquisition in response to the UI action
    // this->runImageAcquisition();
}

