#include "PrepregManager.h"

#include <iostream>
#include <QObject>
#include <QApplication>

PrepregManager::PrepregManager(int argc, char** argv)
    : m_frameGrabber(std::make_unique<HVIS::EuresysFrameGrabber>())
{
    m_app = new QApplication(argc, argv);
    m_uiManager = std::make_unique<MainWindow>();

    QObject::connect(m_uiManager.get(), SIGNAL(actionTriggered()), this, SLOT(doSomethingOnTheInterface()));
}

PrepregManager::~PrepregManager() 
{
    delete m_app;
}

int PrepregManager::show()
{
    m_uiManager->show();
    return m_app->exec();
}

void PrepregManager::runImageAcquisition()
{
    m_frameGrabber->initialize(HVIS::EuresysFrameGrabber::EuresysProducer::Playlink);
    m_frameGrabber->selectCamera(0);
    m_frameGrabber->setCallback([](void* bufferData, size_t width, size_t height, size_t size, size_t frameId) {
        std::cout << "Frame received: ID=" << frameId << ", Size=" << size << ", Resolution=" << width << "x" << height << std::endl;
    });
    m_frameGrabber->allocBuffers(10);
    m_frameGrabber->startAcquisition();
}

void PrepregManager::doSomethingOnTheInterface()
{
    std::cout << "Action triggered from the interface!" << std::endl;
    this->runImageAcquisition();
}

