{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "qt6_add_lupdate", "qt6_add_translations"], "files": ["C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools/Qt6LinguistToolsMacros.cmake:817:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 2, "file": 1, "line": 1, "parent": 1}, {"command": 1, "file": 0, "line": 828, "parent": 2}, {"command": 0, "file": 0, "line": 358, "parent": 3}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "HVIS_UserInterface_lupdate::@6890427a1f51a3e7e1df", "name": "HVIS_UserInterface_lupdate", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "build/CMakeFiles/HVIS_UserInterface_lupdate", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/2a0d95013f98d9338d753b6b55f2113b/HVIS_UserInterface_lupdate.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}