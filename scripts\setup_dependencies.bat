@echo off
setlocal enabledelayedexpansion
call :setESC

echo %ESC%[96m========================================%ESC%[0m
echo %ESC%[96m    HVIS Suite - Dependency Setup     %ESC%[0m
echo %ESC%[96m========================================%ESC%[0m
echo.

:: Parse command line arguments
set FORCE_REINSTALL=OFF
set BUILD_TYPE=Release
set VERBOSE=OFF

:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="--force" set FORCE_REINSTALL=ON
if /i "%~1"=="-f" set FORCE_REINSTALL=ON
if /i "%~1"=="--debug" set BUILD_TYPE=Debug
if /i "%~1"=="-d" set BUILD_TYPE=Debug
if /i "%~1"=="--verbose" set VERBOSE=ON
if /i "%~1"=="-v" set VERBOSE=ON
if /i "%~1"=="--help" goto :show_help
if /i "%~1"=="-h" goto :show_help
shift
goto :parse_args

:done_parsing

echo %ESC%[93mConfiguration:%ESC%[0m
echo   Build Type: %BUILD_TYPE%
echo   Force Reinstall: %FORCE_REINSTALL%
echo   Verbose Output: %VERBOSE%
echo.

:: Change to project root directory
cd /d "%~dp0\.."

:: Check if Conan is installed
echo %ESC%[94mChecking Conan installation...%ESC%[0m
conan --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %ESC%[91mError: Conan is not installed or not in PATH%ESC%[0m
    echo Please install Conan: pip install conan
    pause
    exit /b 1
)

:: Get Conan version for information
for /f "tokens=*" %%i in ('conan --version 2^>nul') do set CONAN_VERSION=%%i
echo %ESC%[92mFound: %CONAN_VERSION%%ESC%[0m
echo.

:: Create build directory if it doesn't exist
if not exist build (
    echo %ESC%[94mCreating build directory...%ESC%[0m
    mkdir build
)

:: Check if dependencies are already installed (unless force reinstall)
if "%FORCE_REINSTALL%"=="OFF" (
    if exist "build\conan_toolchain.cmake" (
        echo %ESC%[93mDependencies appear to be already installed.%ESC%[0m
        echo Use --force or -f to reinstall dependencies.
        echo Use --help or -h for more options.
        echo.
        goto :check_profile
    )
)

:: Clean build directory if force reinstall
if "%FORCE_REINSTALL%"=="ON" (
    echo %ESC%[94mForce reinstall requested - cleaning build directory...%ESC%[0m
    if exist build\conan* del /q build\conan*
    if exist build\*-Target-*.cmake del /q build\*-Target-*.cmake
    if exist build\*Config*.cmake del /q build\*Config*.cmake
    if exist build\*Targets.cmake del /q build\*Targets.cmake
    if exist build\Find*.cmake del /q build\Find*.cmake
    if exist build\module-*.cmake del /q build\module-*.cmake
    if exist build\*-config*.cmake del /q build\*-config*.cmake
    if exist build\*-release-*.cmake del /q build\*-release-*.cmake
    echo %ESC%[92mBuild directory cleaned.%ESC%[0m
    echo.
)

:check_profile
:: Check if default profile exists, create if not
echo %ESC%[94mChecking Conan profile...%ESC%[0m
conan profile show >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %ESC%[93mDefault profile not found. Creating...%ESC%[0m
    conan profile detect --force
    if %ERRORLEVEL% neq 0 (
        echo %ESC%[91mFailed to create Conan profile%ESC%[0m
        pause
        exit /b %ERRORLEVEL%
    )
    echo %ESC%[92mDefault profile created.%ESC%[0m
) else (
    echo %ESC%[92mDefault profile found.%ESC%[0m
)
echo.

:: Display profile information if verbose
if "%VERBOSE%"=="ON" (
    echo %ESC%[94mConan Profile Information:%ESC%[0m
    conan profile show default
    echo.
)

:: Change to build directory
cd build

:: Install dependencies with Conan
echo %ESC%[94mInstalling dependencies with Conan...%ESC%[0m
echo %ESC%[93mThis may take several minutes on first run...%ESC%[0m
echo.

if "%VERBOSE%"=="ON" (
    conan install .. --output-folder=. --build=missing --settings=build_type=%BUILD_TYPE%
) else (
    conan install .. --output-folder=. --build=missing --settings=build_type=%BUILD_TYPE% >conan_install.log 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo %ESC%[91mConan installation failed with error code %ERRORLEVEL%%ESC%[0m
    if "%VERBOSE%"=="OFF" (
        echo.
        echo %ESC%[93mLast 20 lines of conan_install.log:%ESC%[0m
        powershell -Command "Get-Content conan_install.log | Select-Object -Last 20"
    )
    echo.
    echo %ESC%[93mTroubleshooting tips:%ESC%[0m
    echo - Try running with --verbose flag for more details
    echo - Check your internet connection
    echo - Try running: conan profile detect --force
    echo - Clear Conan cache: conan remove "*" --confirm
    pause
    exit /b %ERRORLEVEL%
)

echo %ESC%[92mDependencies installed successfully!%ESC%[0m
echo.

:: Verify installation
echo %ESC%[94mVerifying installation...%ESC%[0m
if not exist "conan_toolchain.cmake" (
    echo %ESC%[91mError: conan_toolchain.cmake not found%ESC%[0m
    pause
    exit /b 1
)

if not exist "cmakedeps_macros.cmake" (
    echo %ESC%[91mError: cmakedeps_macros.cmake not found%ESC%[0m
    pause
    exit /b 1
)

echo %ESC%[92mInstallation verified successfully!%ESC%[0m
echo.

:: List installed packages
echo %ESC%[94mInstalled packages:%ESC%[0m
for %%f in (*Config.cmake) do (
    set filename=%%f
    set packagename=!filename:Config.cmake=!
    echo   - !packagename!
)
echo.

:: Return to project root
cd ..

echo %ESC%[96m========================================%ESC%[0m
echo %ESC%[92m    Dependencies Setup Complete!      %ESC%[0m
echo %ESC%[96m========================================%ESC%[0m
echo.
echo %ESC%[93mNext steps:%ESC%[0m
echo 1. Run: scripts\build_fast.bat app
echo 2. Or use your IDE to build the project
echo.
echo %ESC%[93mGenerated files in build directory:%ESC%[0m
echo - conan_toolchain.cmake (CMake toolchain)
echo - Various *Config.cmake files (package configs)
echo - conanbuild.bat / conanrun.bat (environment scripts)
echo.

if "%VERBOSE%"=="OFF" (
    echo %ESC%[93mNote: Conan output was logged to build\conan_install.log%ESC%[0m
    echo.
)

pause
goto :eof

:show_help
echo.
echo %ESC%[96mHVIS Suite - Dependency Setup Script%ESC%[0m
echo.
echo %ESC%[93mUsage:%ESC%[0m
echo   setup_dependencies.bat [options]
echo.
echo %ESC%[93mOptions:%ESC%[0m
echo   --force, -f      Force reinstall all dependencies
echo   --debug, -d      Install debug versions of dependencies
echo   --verbose, -v    Show verbose output during installation
echo   --help, -h       Show this help message
echo.
echo %ESC%[93mExamples:%ESC%[0m
echo   setup_dependencies.bat                    # Install release dependencies
echo   setup_dependencies.bat --force           # Force reinstall all dependencies
echo   setup_dependencies.bat --debug --verbose # Install debug deps with verbose output
echo.
echo %ESC%[93mNote:%ESC%[0m
echo This script only needs to be run once, or when dependencies change.
echo After running this script, use build_fast.bat for quick compilation.
echo.
pause
goto :eof

:: Function to set ESC sequence for colored output
:setESC
for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
  set ESC=%%b
  exit /B 0
)
exit /b 0
