set(lupdate_project_file "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/CMakeLists.txt")
set(lupdate_translations "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/HexcelVisionApp_es_ES.ts")
set(lupdate_include_paths "")
set(lupdate_sources "")
set(lupdate_subproject_count 2)

set(lupdate_subproject1_source_dir "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface")
set(lupdate_subproject1_include_paths "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/include_Debug;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include;C:/Program Files/Euresys/eGrabber/include;C:/Qt_6/6.9.0/msvc2022_64/include/QtCore;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Qt_6/6.9.0/msvc2022_64/mkspecs/win32-msvc;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Qt_6/6.9.0/msvc2022_64/include/QtWidgets;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Qt_6/6.9.0/msvc2022_64/include/QtGui;C:/Qt_6/6.9.0/msvc2022_64/include")
set(lupdate_subproject1_sources "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/mocs_compilation_Debug.cpp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/src/mainwindow.cpp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/include_Release/ui_mainwindow.h;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include/mainwindow.h;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/ui/mainwindow.ui;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/include_Debug/ui/ui_mainwindow.h;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/autouic_Debug.stamp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/0c926193afbfc73729bd24d8ef5ea95a/autouic_(CONFIG).stamp.rule")
set(lupdate_subproject1_excluded "")
set(lupdate_subproject1_autogen_dir "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen")

set(lupdate_subproject2_source_dir "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface")
set(lupdate_subproject2_include_paths "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen/include_Debug;C:/Qt_6/6.9.0/msvc2022_64/include/QtCore;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Qt_6/6.9.0/msvc2022_64/mkspecs/win32-msvc;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include;C:/Program Files/Euresys/eGrabber/include;C:/Qt_6/6.9.0/msvc2022_64/include/QtWidgets;C:/Qt_6/6.9.0/msvc2022_64/include;C:/Qt_6/6.9.0/msvc2022_64/include/QtGui;C:/Qt_6/6.9.0/msvc2022_64/include")
set(lupdate_subproject2_sources "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen/mocs_compilation_Debug.cpp;main.cpp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/.qt/rcc/UserInterfaceApp_translations.qrc;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/HexcelVisionApp_es_ES.qm;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/.qt/rcc/qrc_UserInterfaceApp_translations.cpp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen/include_Debug/ui/ui_mainwindow.h;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen/autouic_Debug.stamp;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/a3ac39d91d2173075252beb9614a48a1/HexcelVisionApp_es_ES.qm.rule;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/997dab26d2a4683c7b070ca56dc3fb1c/qrc_UserInterfaceApp_translations.cpp.rule;C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/cf7f32f648246ff0cf66c5ffe7652e82/autouic_(CONFIG).stamp.rule")
set(lupdate_subproject2_excluded "")
set(lupdate_subproject2_autogen_dir "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterfaceApp_autogen")
