{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_add_phony_target_deferred"], "files": ["C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake:287:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 1, "file": 1, "line": 1, "parent": 1}, {"command": 0, "file": 0, "line": 342, "parent": 2}]}, "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "update_translations::@6890427a1f51a3e7e1df", "name": "update_translations", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/CMakeFiles/update_translations", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/2a0d95013f98d9338d753b6b55f2113b/update_translations.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}