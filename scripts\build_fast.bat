@echo off
setlocal enabledelayedexpansion
call :setESC

echo %ESC%[96m========================================%ESC%[0m
echo %ESC%[96m      HVIS Suite - Fast Build         %ESC%[0m
echo %ESC%[96m========================================%ESC%[0m
echo.

:: Parse command line arguments
set BUILD_LIB=ON
set BUILD_APP=OFF
set BUILD_TESTS=OFF
set BUILD_DOCS=OFF
set BUILD_TYPE=Release
set PARALLEL_JOBS=0
set VERBOSE=OFF
set CLEAN_BUILD=OFF

:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="lib" set BUILD_LIB=ON
if /i "%~1"=="app" set BUILD_APP=ON
if /i "%~1"=="test" set BUILD_TESTS=ON
if /i "%~1"=="tests" set BUILD_TESTS=ON
if /i "%~1"=="docs" set BUILD_DOCS=ON
if /i "%~1"=="doc" set BUILD_DOCS=ON
if /i "%~1"=="debug" set BUILD_TYPE=Debug
if /i "%~1"=="release" set BUILD_TYPE=Release
if /i "%~1"=="clean" set CLEAN_BUILD=ON
if /i "%~1"=="--verbose" set VERBOSE=ON
if /i "%~1"=="-v" set VERBOSE=ON
if /i "%~1"=="--help" goto :show_help
if /i "%~1"=="-h" goto :show_help
if /i "%~1"=="--parallel" (
    shift
    set PARALLEL_JOBS=%~1
)
if /i "%~1"=="-j" (
    shift
    set PARALLEL_JOBS=%~1
)
shift
goto :parse_args

:done_parsing

:: Set default parallel jobs if not specified
if %PARALLEL_JOBS%==0 (
    set PARALLEL_JOBS=%NUMBER_OF_PROCESSORS%
)

echo %ESC%[93mBuild Configuration:%ESC%[0m
echo   Library: %BUILD_LIB%
echo   Application: %BUILD_APP%
echo   Tests: %BUILD_TESTS%
echo   Documentation: %BUILD_DOCS%
echo   Build Type: %BUILD_TYPE%
echo   Parallel Jobs: %PARALLEL_JOBS%
echo   Clean Build: %CLEAN_BUILD%
echo.

:: Change to project root directory
cd /d "%~dp0\.."

:: Check if dependencies are installed
if not exist "build\conan_toolchain.cmake" (
    echo %ESC%[91mError: Dependencies not found!%ESC%[0m
    echo.
    echo %ESC%[93mPlease run setup_dependencies.bat first:%ESC%[0m
    echo   scripts\setup_dependencies.bat
    echo.
    echo This will install all required dependencies via Conan.
    pause
    exit /b 1
)

:: Clean build if requested
if "%CLEAN_BUILD%"=="ON" (
    echo %ESC%[94mCleaning build directory (preserving dependencies)...%ESC%[0m
    cd build
    if exist CMakeCache.txt del CMakeCache.txt
    if exist CMakeFiles rmdir /s /q CMakeFiles
    if exist HVIS_Suite.dir rmdir /s /q HVIS_Suite.dir
    if exist HVIS_Suite_autogen rmdir /s /q HVIS_Suite_autogen
    if exist *.vcxproj del *.vcxproj
    if exist *.vcxproj.filters del *.vcxproj.filters
    if exist *.sln del *.sln
    if exist bin rmdir /s /q bin
    if exist lib rmdir /s /q lib
    if exist x64 rmdir /s /q x64
    cd ..
    echo %ESC%[92mBuild directory cleaned.%ESC%[0m
    echo.
)

:: Create build directory if it doesn't exist
if not exist build mkdir build
cd build

:: Configure with CMake (only if needed or if clean build)
set NEED_CONFIGURE=OFF
if not exist "CMakeCache.txt" set NEED_CONFIGURE=ON
if "%CLEAN_BUILD%"=="ON" set NEED_CONFIGURE=ON

if "%NEED_CONFIGURE%"=="ON" (
    echo %ESC%[94mConfiguring with CMake...%ESC%[0m
    if "%VERBOSE%"=="ON" (
        cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake -DBUILD_APP=%BUILD_APP% -DBUILD_TESTS=%BUILD_TESTS% -DBUILD_DOCS=%BUILD_DOCS%
    ) else (
        cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake -DBUILD_APP=%BUILD_APP% -DBUILD_TESTS=%BUILD_TESTS% -DBUILD_DOCS=%BUILD_DOCS% >cmake_configure.log 2>&1
    )
    
    if %ERRORLEVEL% neq 0 (
        echo %ESC%[91mCMake configuration failed with error code %ERRORLEVEL%%ESC%[0m
        if "%VERBOSE%"=="OFF" (
            echo.
            echo %ESC%[93mLast 10 lines of cmake_configure.log:%ESC%[0m
            powershell -Command "Get-Content cmake_configure.log -Tail 10"
        )
        pause
        exit /b %ERRORLEVEL%
    )
    echo %ESC%[92mCMake configuration completed.%ESC%[0m
) else (
    echo %ESC%[93mSkipping CMake configuration (already configured).%ESC%[0m
)
echo.

:: Build the project
echo %ESC%[94mBuilding project with %PARALLEL_JOBS% parallel jobs...%ESC%[0m
set BUILD_START_TIME=%TIME%

if "%VERBOSE%"=="ON" (
    cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS%
) else (
    cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS% >cmake_build.log 2>&1
)

if %ERRORLEVEL% neq 0 (
    echo %ESC%[91mBuild failed with error code %ERRORLEVEL%%ESC%[0m
    if "%VERBOSE%"=="OFF" (
        echo.
        echo %ESC%[93mLast 15 lines of cmake_build.log:%ESC%[0m
        powershell -Command "Get-Content cmake_build.log -Tail 15"
    )
    echo.
    echo %ESC%[93mTroubleshooting tips:%ESC%[0m
    echo - Try running with --verbose flag for more details
    echo - Try a clean build: build_fast.bat clean app
    echo - Check if dependencies are up to date: setup_dependencies.bat --force
    pause
    exit /b %ERRORLEVEL%
)

set BUILD_END_TIME=%TIME%
echo %ESC%[92mBuild completed successfully!%ESC%[0m
echo.

:: Calculate build time (simplified)
echo %ESC%[94mBuild completed at %BUILD_END_TIME%%ESC%[0m
echo.

:: Run tests if they were built
if "%BUILD_TESTS%"=="ON" (
    echo %ESC%[94mRunning tests...%ESC%[0m
    ctest -C %BUILD_TYPE% --output-on-failure
    if %ERRORLEVEL% neq 0 (
        echo %ESC%[91mTests failed with error code %ERRORLEVEL%%ESC%[0m
        pause
        exit /b %ERRORLEVEL%
    )
    echo %ESC%[92mAll tests passed!%ESC%[0m
    echo.
)

:: Build documentation if requested
if "%BUILD_DOCS%"=="ON" (
    echo %ESC%[94mBuilding documentation...%ESC%[0m
    cmake --build . --target docs
    if %ERRORLEVEL% neq 0 (
        echo %ESC%[91mDocumentation generation failed with error code %ERRORLEVEL%%ESC%[0m
        pause
        exit /b %ERRORLEVEL%
    )
    echo %ESC%[92mDocumentation generated successfully!%ESC%[0m
    echo.
)

:: Return to original directory
cd ..

:: Display results
echo %ESC%[96m========================================%ESC%[0m
echo %ESC%[92m        Build Completed Successfully!   %ESC%[0m
echo %ESC%[96m========================================%ESC%[0m
echo.

if "%BUILD_APP%"=="ON" (
    if exist "build\bin\%BUILD_TYPE%\HVIS_Suite.exe" (
        echo %ESC%[93mApplication built:%ESC%[0m build\bin\%BUILD_TYPE%\HVIS_Suite.exe
    ) else (
        echo %ESC%[93mApplication location:%ESC%[0m build\bin\%BUILD_TYPE%\
    )
)

if "%BUILD_TESTS%"=="ON" (
    echo %ESC%[93mTest executables:%ESC%[0m build\bin\%BUILD_TYPE%\
)

if "%BUILD_DOCS%"=="ON" (
    echo %ESC%[93mDocumentation:%ESC%[0m build\docs\html\index.html
)

if "%VERBOSE%"=="OFF" (
    echo.
    echo %ESC%[93mBuild logs saved to:%ESC%[0m
    if exist "build\cmake_configure.log" echo   - build\cmake_configure.log
    if exist "build\cmake_build.log" echo   - build\cmake_build.log
)

echo.
pause
goto :eof

:show_help
echo.
echo %ESC%[96mHVIS Suite - Fast Build Script%ESC%[0m
echo.
echo %ESC%[93mUsage:%ESC%[0m
echo   build_fast.bat [targets] [options]
echo.
echo %ESC%[93mTargets:%ESC%[0m
echo   lib              Build library (default: ON)
echo   app              Build application
echo   test, tests      Build and run tests
echo   doc, docs        Build documentation
echo.
echo %ESC%[93mBuild Types:%ESC%[0m
echo   release          Release build (default)
echo   debug            Debug build
echo.
echo %ESC%[93mOptions:%ESC%[0m
echo   clean            Clean build (remove build artifacts, keep dependencies)
echo   --verbose, -v    Show verbose build output
echo   --parallel N, -j N  Use N parallel jobs (default: CPU count)
echo   --help, -h       Show this help message
echo.
echo %ESC%[93mExamples:%ESC%[0m
echo   build_fast.bat app                    # Build application (release)
echo   build_fast.bat app debug              # Build application (debug)
echo   build_fast.bat app test               # Build app and run tests
echo   build_fast.bat clean app --verbose    # Clean build with verbose output
echo   build_fast.bat app -j 4               # Build with 4 parallel jobs
echo.
echo %ESC%[93mNote:%ESC%[0m
echo This script assumes dependencies are already installed.
echo If you get dependency errors, run: scripts\setup_dependencies.bat
echo.
pause
goto :eof

:: Function to set ESC sequence for colored output
:setESC
for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
  set ESC=%%b
  exit /B 0
)
exit /b 0
