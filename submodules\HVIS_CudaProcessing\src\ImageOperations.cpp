#include "ImageOperations.h"
#include <iostream>


void splitImageCUDA(
    uint8_t* gpuPtr, 
    int width,
    int height,
    int channels,
    int type,
    int step,
    int tileWidth,
    int tileHeight
) {
    std::vector<cv::cuda::GpuMat> tiles;

    // Wrap the raw GPU pointer in a GpuMat with explicit step
    cv::cuda::GpuMat d_image(height, width, type, gpuPtr, step);

    int numTilesX = width / tileWidth;
    int numTilesY = height / tileHeight;

    for (int row = 0; row < numTilesY; ++row)
    {
        for (int col = 0; col < numTilesX; ++col)
        {
            cv::Rect roi(col * tileWidth, row * tileHeight, tileWidth, tileHeight);
            tiles.push_back(d_image(roi));
        }
    }

    // return tiles;
}