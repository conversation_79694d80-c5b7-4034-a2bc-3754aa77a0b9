trigger:
  - main

pool:
  vmImage: 'windows-latest'

steps:
- script: |
    choco install doxygen.install
    choco install graphviz
  displayName: 'Install Doxygen and Graphviz'

- script: |
    doxygen Doxyfile
  workingDirectory: '$(Build.SourcesDirectory)'
  displayName: 'Run Doxygen'

- task: CopyFiles@2
  inputs:
    SourceFolder: '$(Build.SourcesDirectory)/docs/html'
    Contents: '**'
    TargetFolder: '$(Build.ArtifactStagingDirectory)/docs'
  displayName: 'Copy HTML documentation'

- task: PublishBuildArtifacts@1
  inputs:
    pathToPublish: '$(Build.ArtifactStagingDirectory)/docs'
    artifactName: 'Documentation'
    publishLocation: 'Container'
  displayName: 'Publish Documentation Artifact'