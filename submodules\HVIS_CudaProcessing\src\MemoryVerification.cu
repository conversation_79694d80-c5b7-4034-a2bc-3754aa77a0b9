/**
 * @file MemoryVerification.cu
 * @brief CUDA kernels for verifying shared memory accessibility
 */

#include <cuda_runtime.h>
#include <iostream>

/**
 * @brief CUDA kernel to test device-side memory access
 * @param ptr Pointer to test
 * @param width Image width
 * @param height Image height
 * @param channels Number of channels
 * @param testPattern Pattern to write for verification
 */
__global__ void testDeviceMemoryAccess(uint8_t* ptr, int width, int height, int channels, uint8_t testPattern) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int totalPixels = width * height * channels;
    
    if (idx < totalPixels && idx < 1000) { // Limit test to first 1000 bytes
        // Write test pattern
        ptr[idx] = testPattern + (idx % 256);
    }
}

/**
 * @brief CUDA kernel to verify memory pattern
 * @param ptr Pointer to verify
 * @param width Image width
 * @param height Image height
 * @param channels Number of channels
 * @param testPattern Expected pattern
 * @param errorCount Output parameter for error count
 */
__global__ void verifyDeviceMemoryPattern(uint8_t* ptr, int width, int height, int channels, 
                                         uint8_t testPattern, int* errorCount) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int totalPixels = width * height * channels;
    
    if (idx < totalPixels && idx < 1000) { // Limit test to first 1000 bytes
        uint8_t expected = testPattern + (idx % 256);
        if (ptr[idx] != expected) {
            atomicAdd(errorCount, 1);
        }
    }
}

/**
 * @brief Host function to test device memory access using CUDA kernels
 * @param ptr Pointer to test
 * @param width Image width
 * @param height Image height
 * @param channels Number of channels
 * @return true if device can access memory correctly
 */
extern "C" bool testDeviceMemoryAccessKernel(uint8_t* ptr, int width, int height, int channels) {
    std::cout << "[CUDA KERNEL TEST] Testing device memory access..." << std::endl;
    
    // Test parameters
    const uint8_t testPattern = 0xAB;
    const int testSize = 1000;
    
    // Launch kernel to write test pattern
    dim3 blockSize(256);
    dim3 gridSize((testSize + blockSize.x - 1) / blockSize.x);
    
    std::cout << "  - Writing test pattern from device..." << std::endl;
    testDeviceMemoryAccess<<<gridSize, blockSize>>>(ptr, width, height, channels, testPattern);
    
    cudaError_t result = cudaDeviceSynchronize();
    if (result != cudaSuccess) {
        std::cout << "  - Device write failed: " << cudaGetErrorString(result) << std::endl;
        return false;
    }
    
    // Allocate device memory for error count
    int* d_errorCount;
    result = cudaMalloc(&d_errorCount, sizeof(int));
    if (result != cudaSuccess) {
        std::cout << "  - Failed to allocate error counter: " << cudaGetErrorString(result) << std::endl;
        return false;
    }
    
    // Initialize error count to zero
    result = cudaMemset(d_errorCount, 0, sizeof(int));
    if (result != cudaSuccess) {
        std::cout << "  - Failed to initialize error counter: " << cudaGetErrorString(result) << std::endl;
        cudaFree(d_errorCount);
        return false;
    }
    
    std::cout << "  - Verifying pattern from device..." << std::endl;
    verifyDeviceMemoryPattern<<<gridSize, blockSize>>>(ptr, width, height, channels, testPattern, d_errorCount);
    
    result = cudaDeviceSynchronize();
    if (result != cudaSuccess) {
        std::cout << "  - Device verification failed: " << cudaGetErrorString(result) << std::endl;
        cudaFree(d_errorCount);
        return false;
    }
    
    // Copy error count back to host
    int hostErrorCount = 0;
    result = cudaMemcpy(&hostErrorCount, d_errorCount, sizeof(int), cudaMemcpyDeviceToHost);
    cudaFree(d_errorCount);
    
    if (result != cudaSuccess) {
        std::cout << "  - Failed to copy error count: " << cudaGetErrorString(result) << std::endl;
        return false;
    }
    
    std::cout << "  - Errors found: " << hostErrorCount << " out of " << testSize << " bytes" << std::endl;
    
    // Also verify from host side
    std::cout << "  - Verifying pattern from host..." << std::endl;
    int hostErrors = 0;
    for (int i = 0; i < testSize; ++i) {
        uint8_t expected = testPattern + (i % 256);
        if (ptr[i] != expected) {
            hostErrors++;
        }
    }
    
    std::cout << "  - Host verification errors: " << hostErrors << " out of " << testSize << " bytes" << std::endl;
    
    bool success = (hostErrorCount == 0 && hostErrors == 0);
    std::cout << "  - Device memory access test: " << (success ? "PASS" : "FAIL") << std::endl;
    
    return success;
}

/**
 * @brief Simple kernel to get device information about a pointer
 * @param ptr Pointer to analyze
 * @param info Output array for information [isAccessible, firstByte, lastByte]
 */
__global__ void getDevicePointerInfo(uint8_t* ptr, int* info) {
    if (threadIdx.x == 0 && blockIdx.x == 0) {
        // Try to access the pointer
        info[0] = 1; // Assume accessible
        
        // Try to read first and last bytes (of a small test region)
        try {
            info[1] = ptr[0];
            info[2] = ptr[99]; // Test 100th byte
        } catch (...) {
            info[0] = 0; // Not accessible
            info[1] = -1;
            info[2] = -1;
        }
    }
}

/**
 * @brief Host function to get device pointer information
 * @param ptr Pointer to analyze
 * @return true if pointer is accessible from device
 */
extern "C" bool getDevicePointerInfo(uint8_t* ptr) {
    std::cout << "[DEVICE INFO] Getting device pointer information..." << std::endl;
    
    int* d_info;
    cudaError_t result = cudaMalloc(&d_info, 3 * sizeof(int));
    if (result != cudaSuccess) {
        std::cout << "  - Failed to allocate device info buffer" << std::endl;
        return false;
    }
    
    // Initialize info array
    int hostInfo[3] = {0, 0, 0};
    cudaMemcpy(d_info, hostInfo, 3 * sizeof(int), cudaMemcpyHostToDevice);
    
    // Launch kernel
    getDevicePointerInfo<<<1, 1>>>(ptr, d_info);
    
    result = cudaDeviceSynchronize();
    if (result != cudaSuccess) {
        std::cout << "  - Kernel execution failed: " << cudaGetErrorString(result) << std::endl;
        cudaFree(d_info);
        return false;
    }
    
    // Copy results back
    cudaMemcpy(hostInfo, d_info, 3 * sizeof(int), cudaMemcpyDeviceToHost);
    cudaFree(d_info);
    
    std::cout << "  - Device accessibility: " << (hostInfo[0] ? "YES" : "NO") << std::endl;
    if (hostInfo[0]) {
        std::cout << "  - First byte value: 0x" << std::hex << hostInfo[1] << std::dec << std::endl;
        std::cout << "  - 100th byte value: 0x" << std::hex << hostInfo[2] << std::dec << std::endl;
    }
    
    return hostInfo[0] != 0;
}
