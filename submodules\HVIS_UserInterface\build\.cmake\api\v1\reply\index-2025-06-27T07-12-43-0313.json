{"cmake": {"generator": {"multiConfig": true, "name": "Visual Studio 17 2022", "platform": "x64"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 0, "string": "4.0.0-rc4", "suffix": "rc4"}}, "objects": [{"jsonFile": "codemodel-v2-2cc8bb7359c7d9ec5300.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-f29383495b9767c3b9b9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e9b8eb65e7baf8857578.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-f29383495b9767c3b9b9.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-e9b8eb65e7baf8857578.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-2cc8bb7359c7d9ec5300.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}}}