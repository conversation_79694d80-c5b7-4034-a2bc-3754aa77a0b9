cmake_minimum_required(VERSION 3.16)
project(HVIS_ImageAcquisition LANGUAGES CXX)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Find packages using Conan
find_package(spdlog REQUIRED)
message(STATUS "Found spdlog: ${spdlog_VERSION}")

# Find GTest package
find_package(GTest REQUIRED)
message(STATUS "Found GTest: ${GTest_VERSION}")

# Automatically find all source and header files
file(GLOB_RECURSE SOURCES CONFIGURE_DEPENDS "src/*.cpp")
file(GLOB_RECURSE HEADERS CONFIGURE_DEPENDS "include/*.h" "*.h")

# Define the library
add_library(ImageAcquisition SHARED
  ${SOURCES}
  ${HEADERS}
)

# Enable automatic export of all symbols on Windows
set_target_properties(ImageAcquisition PROPERTIES
  WINDOWS_EXPORT_ALL_SYMBOLS ON
)

target_compile_definitions(ImageAcquisition PRIVATE IMAGEACQUISITION_LIBRARY)

# Link against spdlog
target_link_libraries(ImageAcquisition PUBLIC
    spdlog::spdlog
)
target_include_directories(ImageAcquisition PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    "C:/Program Files/Euresys/eGrabber/include"
)

# Build options - default to building just the library
option(BUILD_APP "Build the application" OFF)
option(BUILD_TESTS "Build the tests" OFF)
option(BUILD_DOCS "Build documentation" OFF)

# Add the executable if requested
if(BUILD_APP)
  add_executable(ImageAcquisitionApp main.cpp)
  
  # Add dependency to ensure correct build order
  add_dependencies(ImageAcquisitionApp ImageAcquisition)
  
  target_link_libraries(ImageAcquisitionApp PRIVATE ImageAcquisition)
  target_include_directories(ImageAcquisitionApp PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)
endif()

# Add tests if requested
if(BUILD_TESTS)
  # Enable testing
  enable_testing()
  
  # Add tests directory
  add_subdirectory(tests)
endif()

# Add Doxygen documentation if requested
if(BUILD_DOCS)
  # Find Doxygen package
  find_package(Doxygen REQUIRED)
  
  # Set Doxygen configuration variables
  set(DOXYGEN_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/docs)
  set(DOXYGEN_PROJECT_NAME "HVIS Image Acquisition")
  set(DOXYGEN_PROJECT_BRIEF "Image acquisition library for HVIS project")
  set(DOXYGEN_EXTRACT_ALL YES)
  set(DOXYGEN_EXTRACT_PRIVATE YES)
  set(DOXYGEN_EXTRACT_PACKAGE YES)
  set(DOXYGEN_EXTRACT_STATIC YES)
  set(DOXYGEN_EXTRACT_LOCAL_CLASSES YES)
  set(DOXYGEN_RECURSIVE YES)
  set(DOXYGEN_USE_MDFILE_AS_MAINPAGE README.md)
  set(DOXYGEN_GENERATE_HTML YES)
  set(DOXYGEN_GENERATE_LATEX NO)
  
  # Add Doxygen target
  doxygen_add_docs(docs
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/README.md
    COMMENT "Generating API documentation with Doxygen"
  )
endif()