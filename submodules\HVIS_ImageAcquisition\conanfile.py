from conan import ConanFile
from conan.tools.cmake import CMake, CMakeToolchain


class HVIS_CudaProcessing(ConanFile):
    name = "HVIS_ImageAcquisition"
    version = "0.1.0"
    settings = "os", "compiler", "build_type", "arch"
    generators = "CMakeDeps"
    requires = "spdlog/1.15.1", "backward-cpp/1.6", "gtest/1.16.0"
    
    def generate(self):
        tc = CMakeToolchain(self)
        tc.blocks.remove("vs_runtime")
        tc.generate()