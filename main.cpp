#include "PrepregManager.h"
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"

int main(int argc, char** argv)
{
#ifdef _WIN32
    // Allocate a console for this application
    AllocConsole();
    // Redirect stdout and stderr to the console
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
#endif

    PrepregManager manager(argc, argv);
    manager.runImageAcquisition();
    // return manager.show();
    return 0;
}
