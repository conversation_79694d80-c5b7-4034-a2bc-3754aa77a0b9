# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 4.0

# HEADERS at CMakeLists.txt:36 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/*.h")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/UserInterface_autogen/include_Release/ui_mainwindow.h"
  "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include/mainwindow.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/cmake.verify_globs")
endif()

# HEADERS at CMakeLists.txt:36 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include/*.h")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/include/mainwindow.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/cmake.verify_globs")
endif()

# SOURCES at CMakeLists.txt:35 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/src/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/src/mainwindow.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/cmake.verify_globs")
endif()

# UI_FILES at CMakeLists.txt:37 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/ui/*.ui")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/ui/mainwindow.ui"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS_Suite/submodules/HVIS_UserInterface/build/CMakeFiles/cmake.verify_globs")
endif()
